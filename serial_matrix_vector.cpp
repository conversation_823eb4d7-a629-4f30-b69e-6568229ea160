#include "utils.h"
#include <iostream>
#include <iomanip>

/**
 * 串行矩阵向量乘法实现
 * 用作性能对比的基准版本
 */
class SerialMatrixVector {
public:
    static Vector multiply(const Matrix& matrix, const Vector& vector) {
        int rows = matrix.size();
        int cols = matrix[0].size();
        
        if (cols != static_cast<int>(vector.size())) {
            throw std::invalid_argument("Matrix columns must equal vector size");
        }
        
        Vector result(rows, 0.0);
        
        // 串行计算矩阵向量乘法
        for (int i = 0; i < rows; ++i) {
            for (int j = 0; j < cols; ++j) {
                result[i] += matrix[i][j] * vector[j];
            }
        }
        
        return result;
    }
    
    static double benchmark(const Matrix& matrix, const Vector& vector, int iterations = 1) {
        Timer timer;
        Vector result;
        
        timer.start();
        for (int iter = 0; iter < iterations; ++iter) {
            result = multiply(matrix, vector);
        }
        double total_time = timer.stop();
        
        return total_time / iterations; // 平均时间
    }
};

int main(int argc, char* argv[]) {
    std::cout << "=== 串行矩阵向量乘法测试 ===" << std::endl;
    
    // 解析命令行参数
    int matrix_size = DEFAULT_MATRIX_SIZE;
    int iterations = 5;
    
    if (argc > 1) {
        matrix_size = std::atoi(argv[1]);
    }
    if (argc > 2) {
        iterations = std::atoi(argv[2]);
    }
    
    std::cout << "矩阵大小: " << matrix_size << "x" << matrix_size << std::endl;
    std::cout << "迭代次数: " << iterations << std::endl;
    
    try {
        // 生成测试数据
        std::cout << "\n正在生成测试数据..." << std::endl;
        Matrix matrix = MatrixUtils::generateRandomMatrix(matrix_size, matrix_size, 0.0, 1.0);
        Vector vector = MatrixUtils::generateRandomVector(matrix_size, 0.0, 1.0);
        
        // 保存测试数据以供其他程序使用
        MatrixUtils::saveMatrixToFile(matrix, "test_matrix.dat");
        MatrixUtils::saveVectorToFile(vector, "test_vector.dat");
        
        // 显示部分数据
        if (matrix_size <= 10) {
            MatrixUtils::printMatrix(matrix, "测试矩阵");
            MatrixUtils::printVector(vector, "测试向量");
        }
        
        // 执行串行计算
        std::cout << "\n正在执行串行计算..." << std::endl;
        double avg_time = SerialMatrixVector::benchmark(matrix, vector, iterations);
        
        // 计算单次结果用于验证
        Vector result = SerialMatrixVector::multiply(matrix, vector);
        
        // 保存结果
        MatrixUtils::saveVectorToFile(result, "serial_result.dat");
        
        // 显示结果
        if (matrix_size <= 10) {
            MatrixUtils::printVector(result, "计算结果");
        }
        
        // 性能统计
        std::cout << "\n=== 性能统计 ===" << std::endl;
        std::cout << std::fixed << std::setprecision(6);
        std::cout << "平均执行时间: " << avg_time << " 秒" << std::endl;
        std::cout << "计算吞吐量: " << (2.0 * matrix_size * matrix_size) / avg_time / 1e9 
                  << " GFLOPS" << std::endl;
        
        // 保存性能结果
        std::vector<PerformanceResult> results;
        PerformanceResult serial_result;
        serial_result.method = "Serial";
        serial_result.matrix_size = matrix_size;
        serial_result.num_threads = 1;
        serial_result.execution_time = avg_time;
        serial_result.speedup = 1.0;
        serial_result.efficiency = 1.0;
        results.push_back(serial_result);
        
        MatrixUtils::savePerformanceResults(results, "serial_performance.csv");
        
        std::cout << "\n串行计算完成！结果已保存到文件。" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "错误: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
