#!/usr/bin/env python3
"""
Final Matrix-Vector Multiplication Performance Visualization
Generate clean charts showing Serial, OpenMP, and MPI comparison
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

# Set professional font and style
plt.rcParams['font.family'] = ['Times New Roman', 'serif']
plt.rcParams['font.size'] = 12
plt.rcParams['axes.labelsize'] = 14
plt.rcParams['axes.titlesize'] = 16
plt.rcParams['xtick.labelsize'] = 12
plt.rcParams['ytick.labelsize'] = 12
plt.rcParams['legend.fontsize'] = 12
plt.rcParams['figure.titlesize'] = 18
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['axes.grid'] = True
plt.rcParams['grid.alpha'] = 0.3
plt.rcParams['axes.axisbelow'] = True
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300
plt.rcParams['savefig.bbox'] = 'tight'

class FinalVisualizer:
    def __init__(self):
        self.colors = {
            'Serial': '#2E86AB',
            'OpenMP': '#F18F01', 
            'MPI': '#6A994E'
        }
        
    def load_and_process_data(self):
        """Load and process performance data"""
        all_data = []
        
        # Load serial data
        try:
            serial_df = pd.read_csv('serial_performance.csv')
            all_data.append(serial_df)
        except:
            print("Warning: Could not load serial_performance.csv")
        
        # Load OpenMP data - use only the optimized version
        try:
            openmp_df = pd.read_csv('openmp_performance.csv')
            # Filter to get only OpenMP_Optimized and rename it to OpenMP
            optimized = openmp_df[openmp_df['Method'] == 'OpenMP_Optimized'].copy()
            if not optimized.empty:
                optimized['Method'] = 'OpenMP'
                all_data.append(optimized)
            else:
                # If no optimized version, use the best OpenMP result
                openmp_best = openmp_df[openmp_df['Method'] == 'OpenMP'].copy()
                if not openmp_best.empty:
                    # Get the best performing configuration
                    best_idx = openmp_best['ExecutionTime'].idxmin()
                    best_result = openmp_best.loc[[best_idx]]
                    all_data.append(best_result)
        except:
            print("Warning: Could not load openmp_performance.csv")
        
        # Load MPI data
        try:
            mpi_df = pd.read_csv('mpi_performance.csv')
            all_data.append(mpi_df)
        except:
            print("Warning: Could not load mpi_performance.csv")
        
        if all_data:
            return pd.concat(all_data, ignore_index=True)
        else:
            return None
    
    def plot_execution_time_comparison(self, data):
        """Create execution time comparison chart"""
        fig, ax = plt.subplots(figsize=(10, 6))
        
        methods = ['Serial', 'OpenMP', 'MPI']
        times = []
        threads = []
        colors = []
        
        for method in methods:
            method_data = data[data['Method'] == method]
            if not method_data.empty:
                best_idx = method_data['ExecutionTime'].idxmin()
                times.append(method_data.loc[best_idx, 'ExecutionTime'])
                threads.append(method_data.loc[best_idx, 'NumThreads'])
                colors.append(self.colors[method])
            else:
                times.append(0)
                threads.append(0)
                colors.append('#CCCCCC')
        
        bars = ax.bar(methods, times, color=colors, alpha=0.8, edgecolor='white', linewidth=2)
        
        # Add value labels
        for i, (bar, time, thread) in enumerate(zip(bars, times, threads)):
            if time > 0:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + height*0.02,
                       f'{time:.6f}s\n({thread} threads)', 
                       ha='center', va='bottom', fontweight='bold')
        
        ax.set_ylabel('Execution Time (seconds)', fontweight='bold')
        ax.set_title('Performance Comparison: Matrix-Vector Multiplication\nExecution Time by Method', 
                    fontweight='bold', pad=20)
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        
        plt.tight_layout()
        plt.savefig('execution_time_comparison.png', dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()
        print("Updated: execution_time_comparison.png")
    
    def plot_speedup_comparison(self, data):
        """Create speedup comparison chart"""
        fig, ax = plt.subplots(figsize=(10, 6))
        
        methods = ['Serial', 'OpenMP', 'MPI']
        speedups = []
        efficiencies = []
        
        for method in methods:
            method_data = data[data['Method'] == method]
            if not method_data.empty:
                best_idx = method_data['ExecutionTime'].idxmin()
                speedups.append(method_data.loc[best_idx, 'Speedup'])
                efficiencies.append(method_data.loc[best_idx, 'Efficiency'] * 100)
            else:
                speedups.append(0)
                efficiencies.append(0)
        
        x = np.arange(len(methods))
        width = 0.35
        
        bars1 = ax.bar(x - width/2, speedups, width, label='Speedup', 
                      color=[self.colors[m] for m in methods], alpha=0.8)
        bars2 = ax.bar(x + width/2, efficiencies, width, label='Efficiency (%)', 
                      color=[self.colors[m] for m in methods], alpha=0.6)
        
        # Add value labels
        for bars in [bars1, bars2]:
            for bar in bars:
                height = bar.get_height()
                if height > 0:
                    ax.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                           f'{height:.1f}', ha='center', va='bottom', fontweight='bold')
        
        ax.set_ylabel('Value', fontweight='bold')
        ax.set_title('Speedup and Parallel Efficiency Comparison', fontweight='bold', pad=20)
        ax.set_xticks(x)
        ax.set_xticklabels(methods)
        ax.legend()
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        
        plt.tight_layout()
        plt.savefig('speedup_analysis.png', dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()
        print("Updated: speedup_analysis.png")
    
    def plot_throughput_comparison(self, data):
        """Create throughput comparison chart"""
        fig, ax = plt.subplots(figsize=(10, 6))
        
        matrix_size = data['MatrixSize'].iloc[0]
        methods = ['Serial', 'OpenMP', 'MPI']
        throughputs = []
        
        for method in methods:
            method_data = data[data['Method'] == method]
            if not method_data.empty:
                best_idx = method_data['ExecutionTime'].idxmin()
                exec_time = method_data.loc[best_idx, 'ExecutionTime']
                throughput = (2.0 * matrix_size * matrix_size) / exec_time / 1e9
                throughputs.append(throughput)
            else:
                throughputs.append(0)
        
        bars = ax.bar(methods, throughputs, 
                     color=[self.colors[m] for m in methods], 
                     alpha=0.8, edgecolor='white', linewidth=2)
        
        # Add value labels
        for bar, throughput in zip(bars, throughputs):
            if throughput > 0:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + height*0.02,
                       f'{throughput:.2f}', ha='center', va='bottom', fontweight='bold')
        
        ax.set_ylabel('Computational Throughput (GFLOPS)', fontweight='bold')
        ax.set_title('Computational Throughput Comparison\nMatrix-Vector Multiplication', 
                    fontweight='bold', pad=20)
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        
        plt.tight_layout()
        plt.savefig('throughput_comparison.png', dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()
        print("Updated: throughput_comparison.png")
    
    def plot_comprehensive_dashboard(self, data):
        """Create comprehensive performance dashboard"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(14, 10))
        
        matrix_size = data['MatrixSize'].iloc[0]
        methods = ['Serial', 'OpenMP', 'MPI']
        
        # Collect data
        times, speedups, efficiencies, throughputs = [], [], [], []
        threads_list = []
        
        for method in methods:
            method_data = data[data['Method'] == method]
            if not method_data.empty:
                best_idx = method_data['ExecutionTime'].idxmin()
                times.append(method_data.loc[best_idx, 'ExecutionTime'])
                speedups.append(method_data.loc[best_idx, 'Speedup'])
                efficiencies.append(method_data.loc[best_idx, 'Efficiency'] * 100)
                exec_time = method_data.loc[best_idx, 'ExecutionTime']
                throughput = (2.0 * matrix_size * matrix_size) / exec_time / 1e9
                throughputs.append(throughput)
                threads_list.append(method_data.loc[best_idx, 'NumThreads'])
            else:
                times.append(0)
                speedups.append(0)
                efficiencies.append(0)
                throughputs.append(0)
                threads_list.append(0)
        
        colors = [self.colors[m] for m in methods]
        
        # Plot 1: Execution Time
        bars1 = ax1.bar(methods, times, color=colors, alpha=0.8)
        for bar, time in zip(bars1, times):
            if time > 0:
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.02,
                        f'{time:.6f}s', ha='center', va='bottom', fontsize=10)
        ax1.set_ylabel('Time (s)')
        ax1.set_title('A. Execution Time', fontweight='bold', loc='left')
        ax1.spines['top'].set_visible(False)
        ax1.spines['right'].set_visible(False)
        
        # Plot 2: Speedup
        bars2 = ax2.bar(methods, speedups, color=colors, alpha=0.8)
        for bar, speedup in zip(bars2, speedups):
            if speedup > 0:
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + height*0.02,
                        f'{speedup:.2f}x', ha='center', va='bottom', fontsize=10)
        ax2.set_ylabel('Speedup')
        ax2.set_title('B. Speedup Factor', fontweight='bold', loc='left')
        ax2.spines['top'].set_visible(False)
        ax2.spines['right'].set_visible(False)
        
        # Plot 3: Efficiency
        bars3 = ax3.bar(methods, efficiencies, color=colors, alpha=0.8)
        for bar, eff in zip(bars3, efficiencies):
            if eff > 0:
                height = bar.get_height()
                ax3.text(bar.get_x() + bar.get_width()/2., height + height*0.02,
                        f'{eff:.1f}%', ha='center', va='bottom', fontsize=10)
        ax3.axhline(y=100, color='red', linestyle='--', alpha=0.7, label='100% Efficiency')
        ax3.set_ylabel('Efficiency (%)')
        ax3.set_title('C. Parallel Efficiency', fontweight='bold', loc='left')
        ax3.spines['top'].set_visible(False)
        ax3.spines['right'].set_visible(False)
        
        # Plot 4: Throughput
        bars4 = ax4.bar(methods, throughputs, color=colors, alpha=0.8)
        for bar, throughput in zip(bars4, throughputs):
            if throughput > 0:
                height = bar.get_height()
                ax4.text(bar.get_x() + bar.get_width()/2., height + height*0.02,
                        f'{throughput:.2f}', ha='center', va='bottom', fontsize=10)
        ax4.set_ylabel('GFLOPS')
        ax4.set_title('D. Computational Throughput', fontweight='bold', loc='left')
        ax4.spines['top'].set_visible(False)
        ax4.spines['right'].set_visible(False)
        
        plt.suptitle('Matrix-Vector Multiplication: Comprehensive Performance Analysis', 
                    fontsize=16, fontweight='bold', y=0.98)
        plt.tight_layout()
        plt.savefig('comprehensive_performance.png', dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()
        print("Updated: comprehensive_performance.png")

def main():
    print("Final Matrix-Vector Multiplication Performance Visualization")
    print("=" * 60)
    
    visualizer = FinalVisualizer()
    data = visualizer.load_and_process_data()
    
    if data is None or data.empty:
        print("No performance data available.")
        return
    
    print(f"Loaded {len(data)} performance records")
    print("Generating updated visualization charts...")
    
    # Generate all charts
    visualizer.plot_execution_time_comparison(data)
    visualizer.plot_speedup_comparison(data)
    visualizer.plot_throughput_comparison(data)
    visualizer.plot_comprehensive_dashboard(data)
    
    print("\nAll charts updated successfully!")
    print("Charts show comparison between Serial, OpenMP, and MPI methods.")

if __name__ == "__main__":
    main()
