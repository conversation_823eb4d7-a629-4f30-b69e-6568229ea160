#!/usr/bin/env python3
"""
Premium Matrix-Vector Multiplication Performance Visualizer
High-end visualization with professional design and optimized layout
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
from matplotlib.patches import Rectangle
import matplotlib.patches as mpatches

# Premium styling configuration
plt.style.use('seaborn-v0_8-whitegrid')
plt.rcParams.update({
    'font.family': ['DejaVu Serif', 'Times New Roman', 'serif'],
    'font.size': 11,
    'axes.labelsize': 13,
    'axes.titlesize': 15,
    'xtick.labelsize': 11,
    'ytick.labelsize': 11,
    'legend.fontsize': 11,
    'figure.titlesize': 17,
    'axes.linewidth': 1.2,
    'grid.linewidth': 0.8,
    'lines.linewidth': 2.5,
    'patch.linewidth': 1.2,
    'xtick.major.width': 1.2,
    'ytick.major.width': 1.2,
    'axes.edgecolor': '#2E2E2E',
    'text.color': '#2E2E2E',
    'axes.labelcolor': '#2E2E2E',
    'xtick.color': '#2E2E2E',
    'ytick.color': '#2E2E2E',
    'grid.color': '#E0E0E0',
    'grid.alpha': 0.6,
    'figure.facecolor': 'white',
    'axes.facecolor': 'white',
    'savefig.facecolor': 'white',
    'savefig.dpi': 300,
    'figure.dpi': 100
})

class PremiumVisualizer:
    def __init__(self):
        # Premium color palette - sophisticated and harmonious
        self.colors = {
            'Serial': '#1B4F72',      # Deep blue
            'OpenMP': '#D35400',      # Vibrant orange
            'MPI': '#27AE60'          # Professional green
        }
        
        # Gradient colors for enhanced visuals
        self.gradients = {
            'Serial': ['#1B4F72', '#2E86AB'],
            'OpenMP': ['#D35400', '#E67E22'],
            'MPI': ['#27AE60', '#2ECC71']
        }
        
        # Professional markers
        self.markers = {
            'Serial': 'o',
            'OpenMP': 's', 
            'MPI': '^'
        }
        
    def load_and_process_data(self):
        """Load and intelligently process performance data"""
        all_data = []
        
        # Load all CSV files
        files = ['serial_performance.csv', 'openmp_performance.csv', 'mpi_performance.csv']
        
        for file in files:
            try:
                df = pd.read_csv(file)
                all_data.append(df)
                print(f"✓ Loaded {file}: {len(df)} records")
            except Exception as e:
                print(f"⚠ Could not load {file}: {e}")
        
        if not all_data:
            return None
            
        combined = pd.concat(all_data, ignore_index=True)
        
        # Intelligent data processing
        processed_data = []
        
        # Serial data
        serial_data = combined[combined['Method'] == 'Serial']
        if not serial_data.empty:
            processed_data.append(serial_data.iloc[0:1])  # Take first record
        
        # OpenMP data - use the best performing version
        openmp_data = combined[combined['Method'].str.contains('OpenMP')]
        if not openmp_data.empty:
            # Get the best execution time across all OpenMP variants
            best_openmp = openmp_data.loc[openmp_data['ExecutionTime'].idxmin()].copy()
            best_openmp['Method'] = 'OpenMP'  # Standardize name
            processed_data.append(pd.DataFrame([best_openmp]))
            
            # Also keep the scaling data for OpenMP
            openmp_scaling = openmp_data[openmp_data['Method'] == 'OpenMP'].copy()
            if not openmp_scaling.empty:
                processed_data.append(openmp_scaling)
        
        # MPI data
        mpi_data = combined[combined['Method'] == 'MPI']
        if not mpi_data.empty:
            processed_data.append(mpi_data)
        
        if processed_data:
            result = pd.concat(processed_data, ignore_index=True)
            # Remove duplicates and keep best performance
            result = result.drop_duplicates(subset=['Method'], keep='first')
            return result
        
        return combined
    
    def create_execution_time_chart(self, data):
        """Create premium execution time comparison chart"""
        fig, ax = plt.subplots(figsize=(12, 7))
        
        # Prepare data
        methods = ['Serial', 'OpenMP', 'MPI']
        times = []
        threads = []
        
        for method in methods:
            method_data = data[data['Method'] == method]
            if not method_data.empty:
                best_idx = method_data['ExecutionTime'].idxmin()
                times.append(method_data.loc[best_idx, 'ExecutionTime'])
                threads.append(int(method_data.loc[best_idx, 'NumThreads']))
            else:
                times.append(0)
                threads.append(0)
        
        # Create sophisticated bar chart
        x_pos = np.arange(len(methods))
        bars = ax.bar(x_pos, times, 
                     color=[self.colors[m] for m in methods],
                     alpha=0.85,
                     edgecolor='white',
                     linewidth=2,
                     width=0.6)
        
        # Add gradient effect
        for i, (bar, method) in enumerate(zip(bars, methods)):
            # Add subtle gradient effect with patches
            height = bar.get_height()
            width = bar.get_width()
            x = bar.get_x()
            
            # Create gradient rectangles
            n_segments = 20
            for j in range(n_segments):
                y_start = j * height / n_segments
                segment_height = height / n_segments
                alpha = 0.3 + 0.7 * (n_segments - j) / n_segments
                
                rect = Rectangle((x, y_start), width, segment_height,
                               facecolor=self.colors[method], alpha=alpha * 0.3,
                               edgecolor='none')
                ax.add_patch(rect)
        
        # Add value labels with enhanced styling
        for i, (bar, time, thread) in enumerate(zip(bars, times, threads)):
            if time > 0:
                height = bar.get_height()
                # Main value
                ax.text(bar.get_x() + bar.get_width()/2., height + max(times)*0.02,
                       f'{time:.6f}s',
                       ha='center', va='bottom', fontweight='bold', fontsize=12,
                       bbox=dict(boxstyle="round,pad=0.3", facecolor='white', 
                                edgecolor=self.colors[methods[i]], alpha=0.9))
                
                # Thread count
                ax.text(bar.get_x() + bar.get_width()/2., height + max(times)*0.08,
                       f'({thread} threads)' if thread > 1 else '(1 thread)',
                       ha='center', va='bottom', fontsize=10, style='italic',
                       color=self.colors[methods[i]])
        
        # Styling
        ax.set_xlabel('Parallelization Method', fontweight='bold', fontsize=14)
        ax.set_ylabel('Execution Time (seconds)', fontweight='bold', fontsize=14)
        ax.set_title('Matrix-Vector Multiplication Performance Comparison\nExecution Time Analysis', 
                    fontweight='bold', fontsize=16, pad=20)
        
        ax.set_xticks(x_pos)
        ax.set_xticklabels(methods, fontweight='bold')
        ax.set_ylim(0, max(times) * 1.15)
        
        # Remove top and right spines
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.spines['left'].set_linewidth(1.2)
        ax.spines['bottom'].set_linewidth(1.2)
        
        plt.tight_layout()
        plt.savefig('execution_time_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("✓ Generated: execution_time_comparison.png")
    
    def create_speedup_efficiency_chart(self, data):
        """Create sophisticated speedup and efficiency analysis"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 7))
        
        # Left plot: Speedup comparison
        methods = ['Serial', 'OpenMP', 'MPI']
        speedups = []
        efficiencies = []
        
        for method in methods:
            method_data = data[data['Method'] == method]
            if not method_data.empty:
                best_idx = method_data['ExecutionTime'].idxmin()
                speedups.append(method_data.loc[best_idx, 'Speedup'])
                efficiencies.append(method_data.loc[best_idx, 'Efficiency'] * 100)
            else:
                speedups.append(1.0 if method == 'Serial' else 0)
                efficiencies.append(100.0 if method == 'Serial' else 0)
        
        # Speedup bars
        x_pos = np.arange(len(methods))
        bars1 = ax1.bar(x_pos, speedups,
                       color=[self.colors[m] for m in methods],
                       alpha=0.8, width=0.6, edgecolor='white', linewidth=2)
        
        # Add ideal speedup reference line
        max_speedup = max(speedups)
        ax1.axhline(y=1, color='gray', linestyle='--', alpha=0.7, linewidth=1.5, label='Baseline (1x)')
        
        # Value labels for speedup
        for bar, speedup in zip(bars1, speedups):
            if speedup > 0:
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height + max_speedup*0.02,
                        f'{speedup:.2f}×',
                        ha='center', va='bottom', fontweight='bold', fontsize=11,
                        bbox=dict(boxstyle="round,pad=0.2", facecolor='white', alpha=0.8))
        
        ax1.set_xlabel('Method', fontweight='bold')
        ax1.set_ylabel('Speedup Factor', fontweight='bold')
        ax1.set_title('Speedup Performance', fontweight='bold', fontsize=14)
        ax1.set_xticks(x_pos)
        ax1.set_xticklabels(methods, fontweight='bold')
        ax1.set_ylim(0, max_speedup * 1.15)
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        
        # Right plot: Efficiency analysis
        bars2 = ax2.bar(x_pos, efficiencies,
                       color=[self.colors[m] for m in methods],
                       alpha=0.8, width=0.6, edgecolor='white', linewidth=2)
        
        # Efficiency reference lines
        ax2.axhline(y=100, color='green', linestyle='-', alpha=0.7, linewidth=2, label='100% Efficiency')
        ax2.axhline(y=80, color='orange', linestyle='--', alpha=0.7, linewidth=1.5, label='80% Threshold')
        ax2.axhline(y=50, color='red', linestyle='--', alpha=0.7, linewidth=1.5, label='50% Threshold')
        
        # Value labels for efficiency
        for bar, eff in zip(bars2, efficiencies):
            if eff > 0:
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + 3,
                        f'{eff:.1f}%',
                        ha='center', va='bottom', fontweight='bold', fontsize=11,
                        bbox=dict(boxstyle="round,pad=0.2", facecolor='white', alpha=0.8))
        
        ax2.set_xlabel('Method', fontweight='bold')
        ax2.set_ylabel('Parallel Efficiency (%)', fontweight='bold')
        ax2.set_title('Parallel Efficiency Analysis', fontweight='bold', fontsize=14)
        ax2.set_xticks(x_pos)
        ax2.set_xticklabels(methods, fontweight='bold')
        ax2.set_ylim(0, max(efficiencies) * 1.2)
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        
        # Remove top and right spines for both plots
        for ax in [ax1, ax2]:
            ax.spines['top'].set_visible(False)
            ax.spines['right'].set_visible(False)
        
        plt.tight_layout()
        plt.savefig('speedup_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("✓ Generated: speedup_analysis.png")
    
    def create_throughput_chart(self, data):
        """Create premium throughput comparison chart"""
        fig, ax = plt.subplots(figsize=(12, 7))
        
        # Calculate throughput
        matrix_size = data['MatrixSize'].iloc[0]
        methods = ['Serial', 'OpenMP', 'MPI']
        throughputs = []
        
        for method in methods:
            method_data = data[data['Method'] == method]
            if not method_data.empty:
                best_idx = method_data['ExecutionTime'].idxmin()
                exec_time = method_data.loc[best_idx, 'ExecutionTime']
                # FLOPS = 2 * n^2 for matrix-vector multiplication
                flops = 2.0 * matrix_size * matrix_size
                throughput = flops / exec_time / 1e9  # GFLOPS
                throughputs.append(throughput)
            else:
                throughputs.append(0)
        
        # Create sophisticated bar chart with gradients
        x_pos = np.arange(len(methods))
        bars = ax.bar(x_pos, throughputs,
                     color=[self.colors[m] for m in methods],
                     alpha=0.85, width=0.6, edgecolor='white', linewidth=2)
        
        # Add performance indicators
        max_throughput = max(throughputs)
        
        # Value labels with enhanced styling
        for i, (bar, throughput) in enumerate(zip(bars, throughputs)):
            if throughput > 0:
                height = bar.get_height()
                # Main value
                ax.text(bar.get_x() + bar.get_width()/2., height + max_throughput*0.02,
                       f'{throughput:.2f}',
                       ha='center', va='bottom', fontweight='bold', fontsize=12,
                       bbox=dict(boxstyle="round,pad=0.3", facecolor='white',
                                edgecolor=self.colors[methods[i]], alpha=0.9))
                
                # Performance category
                if throughput > max_throughput * 0.8:
                    category = "Excellent"
                    cat_color = 'green'
                elif throughput > max_throughput * 0.5:
                    category = "Good"
                    cat_color = 'orange'
                else:
                    category = "Baseline"
                    cat_color = 'gray'
                
                ax.text(bar.get_x() + bar.get_width()/2., height + max_throughput*0.08,
                       category,
                       ha='center', va='bottom', fontsize=10, style='italic',
                       color=cat_color, fontweight='bold')
        
        # Styling
        ax.set_xlabel('Parallelization Method', fontweight='bold', fontsize=14)
        ax.set_ylabel('Computational Throughput (GFLOPS)', fontweight='bold', fontsize=14)
        ax.set_title('Computational Throughput Comparison\nMatrix-Vector Multiplication Performance', 
                    fontweight='bold', fontsize=16, pad=20)
        
        ax.set_xticks(x_pos)
        ax.set_xticklabels(methods, fontweight='bold')
        ax.set_ylim(0, max_throughput * 1.15)
        
        # Add performance zones
        ax.axhspan(0, max_throughput * 0.3, alpha=0.1, color='red', label='Low Performance')
        ax.axhspan(max_throughput * 0.3, max_throughput * 0.7, alpha=0.1, color='yellow', label='Medium Performance')
        ax.axhspan(max_throughput * 0.7, max_throughput * 1.15, alpha=0.1, color='green', label='High Performance')
        
        # Remove top and right spines
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        
        plt.tight_layout()
        plt.savefig('throughput_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("✓ Generated: throughput_comparison.png")

    def create_comprehensive_dashboard(self, data):
        """Create premium comprehensive performance dashboard"""
        fig = plt.figure(figsize=(16, 12))
        gs = fig.add_gridspec(3, 3, height_ratios=[1.2, 1, 1], width_ratios=[1, 1, 1],
                             hspace=0.35, wspace=0.3)

        # Prepare data
        matrix_size = data['MatrixSize'].iloc[0]
        methods = ['Serial', 'OpenMP', 'MPI']

        # Collect all metrics
        times, speedups, efficiencies, throughputs = [], [], [], []
        threads_list = []

        for method in methods:
            method_data = data[data['Method'] == method]
            if not method_data.empty:
                best_idx = method_data['ExecutionTime'].idxmin()
                times.append(method_data.loc[best_idx, 'ExecutionTime'])
                speedups.append(method_data.loc[best_idx, 'Speedup'])
                efficiencies.append(method_data.loc[best_idx, 'Efficiency'] * 100)

                exec_time = method_data.loc[best_idx, 'ExecutionTime']
                flops = 2.0 * matrix_size * matrix_size
                throughput = flops / exec_time / 1e9
                throughputs.append(throughput)
                threads_list.append(int(method_data.loc[best_idx, 'NumThreads']))
            else:
                times.append(0)
                speedups.append(1 if method == 'Serial' else 0)
                efficiencies.append(100 if method == 'Serial' else 0)
                throughputs.append(0)
                threads_list.append(1 if method == 'Serial' else 0)

        # Main title
        fig.suptitle('Matrix-Vector Multiplication: Comprehensive Performance Analysis\n' +
                    f'Matrix Size: {matrix_size}×{matrix_size} | Data Type: Double Precision',
                    fontsize=18, fontweight='bold', y=0.95)

        # 1. Execution Time (top, spanning 2 columns)
        ax1 = fig.add_subplot(gs[0, :2])
        bars1 = ax1.bar(methods, times, color=[self.colors[m] for m in methods],
                       alpha=0.8, edgecolor='white', linewidth=2)

        for bar, time, threads in zip(bars1, times, threads_list):
            if time > 0:
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height + max(times)*0.02,
                        f'{time:.6f}s\n({threads} threads)',
                        ha='center', va='bottom', fontweight='bold', fontsize=10)

        ax1.set_ylabel('Execution Time (s)', fontweight='bold')
        ax1.set_title('A. Execution Time Comparison', fontweight='bold', loc='left', fontsize=14)
        ax1.spines['top'].set_visible(False)
        ax1.spines['right'].set_visible(False)

        # 2. Performance Summary (top right)
        ax2 = fig.add_subplot(gs[0, 2])
        ax2.axis('off')

        # Create performance summary table
        summary_text = "Performance Summary\n" + "="*20 + "\n\n"

        best_method = methods[times.index(min([t for t in times if t > 0]))]
        best_time = min([t for t in times if t > 0])
        best_speedup = max(speedups)
        best_throughput = max(throughputs)

        summary_text += f"🏆 Best Performance:\n   {best_method}\n\n"
        summary_text += f"⚡ Fastest Time:\n   {best_time:.6f}s\n\n"
        summary_text += f"🚀 Max Speedup:\n   {best_speedup:.2f}×\n\n"
        summary_text += f"💪 Peak Throughput:\n   {best_throughput:.2f} GFLOPS"

        ax2.text(0.05, 0.95, summary_text, transform=ax2.transAxes, fontsize=11,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle="round,pad=0.5", facecolor='lightblue', alpha=0.3))

        # 3. Speedup Analysis (middle left)
        ax3 = fig.add_subplot(gs[1, 0])
        bars3 = ax3.bar(methods, speedups, color=[self.colors[m] for m in methods],
                       alpha=0.8, edgecolor='white', linewidth=1.5)

        ax3.axhline(y=1, color='gray', linestyle='--', alpha=0.7, label='Baseline')

        for bar, speedup in zip(bars3, speedups):
            if speedup > 0:
                height = bar.get_height()
                ax3.text(bar.get_x() + bar.get_width()/2., height + max(speedups)*0.02,
                        f'{speedup:.2f}×', ha='center', va='bottom', fontweight='bold', fontsize=9)

        ax3.set_ylabel('Speedup', fontweight='bold')
        ax3.set_title('B. Speedup Factor', fontweight='bold', loc='left', fontsize=12)
        ax3.spines['top'].set_visible(False)
        ax3.spines['right'].set_visible(False)
        ax3.tick_params(axis='x', rotation=45)

        # 4. Efficiency Analysis (middle center)
        ax4 = fig.add_subplot(gs[1, 1])
        bars4 = ax4.bar(methods, efficiencies, color=[self.colors[m] for m in methods],
                       alpha=0.8, edgecolor='white', linewidth=1.5)

        ax4.axhline(y=100, color='green', linestyle='-', alpha=0.7, label='100%')
        ax4.axhline(y=80, color='orange', linestyle='--', alpha=0.7, label='80%')

        for bar, eff in zip(bars4, efficiencies):
            if eff > 0:
                height = bar.get_height()
                ax4.text(bar.get_x() + bar.get_width()/2., height + 2,
                        f'{eff:.1f}%', ha='center', va='bottom', fontweight='bold', fontsize=9)

        ax4.set_ylabel('Efficiency (%)', fontweight='bold')
        ax4.set_title('C. Parallel Efficiency', fontweight='bold', loc='left', fontsize=12)
        ax4.spines['top'].set_visible(False)
        ax4.spines['right'].set_visible(False)
        ax4.tick_params(axis='x', rotation=45)

        # 5. Throughput Analysis (middle right)
        ax5 = fig.add_subplot(gs[1, 2])
        bars5 = ax5.bar(methods, throughputs, color=[self.colors[m] for m in methods],
                       alpha=0.8, edgecolor='white', linewidth=1.5)

        for bar, throughput in zip(bars5, throughputs):
            if throughput > 0:
                height = bar.get_height()
                ax5.text(bar.get_x() + bar.get_width()/2., height + max(throughputs)*0.02,
                        f'{throughput:.2f}', ha='center', va='bottom', fontweight='bold', fontsize=9)

        ax5.set_ylabel('GFLOPS', fontweight='bold')
        ax5.set_title('D. Computational Throughput', fontweight='bold', loc='left', fontsize=12)
        ax5.spines['top'].set_visible(False)
        ax5.spines['right'].set_visible(False)
        ax5.tick_params(axis='x', rotation=45)

        # 6. Performance Radar Chart (bottom)
        ax6 = fig.add_subplot(gs[2, :], projection='polar')

        # Normalize metrics for radar chart
        categories = ['Speed\n(1/Time)', 'Speedup', 'Efficiency', 'Throughput']

        # Normalize values to 0-1 scale
        norm_times = [(max(times) - t) / max(times) if t > 0 else 0 for t in times]  # Invert time
        norm_speedups = [s / max(speedups) if s > 0 else 0 for s in speedups]
        norm_effs = [e / 100 for e in efficiencies]  # Already in percentage
        norm_throughputs = [t / max(throughputs) if t > 0 else 0 for t in throughputs]

        angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
        angles += angles[:1]  # Complete the circle

        for i, method in enumerate(methods):
            if any([norm_times[i], norm_speedups[i], norm_effs[i], norm_throughputs[i]]):
                values = [norm_times[i], norm_speedups[i], norm_effs[i], norm_throughputs[i]]
                values += values[:1]  # Complete the circle

                ax6.plot(angles, values, 'o-', linewidth=2, label=method,
                        color=self.colors[method], markersize=6)
                ax6.fill(angles, values, alpha=0.25, color=self.colors[method])

        ax6.set_xticks(angles[:-1])
        ax6.set_xticklabels(categories, fontsize=10)
        ax6.set_ylim(0, 1)
        ax6.set_title('E. Performance Radar Chart', fontweight='bold', pad=20, fontsize=12)
        ax6.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        ax6.grid(True)

        plt.savefig('comprehensive_performance.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("✓ Generated: comprehensive_performance.png")

def main():
    print("🎨 Premium Matrix-Vector Multiplication Performance Visualizer")
    print("=" * 65)
    
    visualizer = PremiumVisualizer()
    data = visualizer.load_and_process_data()
    
    if data is None or data.empty:
        print("❌ No performance data available. Please run tests first.")
        return
    
    print(f"\n📊 Processing {len(data)} performance records...")
    print("🎯 Generating premium visualization charts...")
    
    # Generate all premium charts
    visualizer.create_execution_time_chart(data)
    visualizer.create_speedup_efficiency_chart(data)
    visualizer.create_throughput_chart(data)
    visualizer.create_comprehensive_dashboard(data)

    print("\n✨ Premium visualization complete!")
    print("📁 Generated files:")
    print("   • execution_time_comparison.png")
    print("   • speedup_analysis.png")
    print("   • throughput_comparison.png")
    print("   • comprehensive_performance.png")
    print("\n🎉 All charts feature premium design with enhanced styling!")
    print("🎨 Professional color scheme and sophisticated layout applied!")
    print("📊 Ready for academic publication and professional presentation!")

if __name__ == "__main__":
    main()
