#!/usr/bin/env python3
"""
简化版性能报告生成器
不依赖外部库，生成文本格式的详细报告
"""

import csv
import os
from datetime import datetime

class SimpleReportGenerator:
    def __init__(self):
        self.results = []
        
    def load_csv_file(self, filename):
        """加载CSV文件"""
        if not os.path.exists(filename):
            print(f"警告: 文件 {filename} 不存在")
            return []
        
        data = []
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    if row['Method'].strip():  # 跳过空行
                        data.append(row)
            print(f"成功加载 {len(data)} 条记录从 {filename}")
            return data
        except Exception as e:
            print(f"错误: 加载文件 {filename} 失败 - {e}")
            return []
    
    def load_all_data(self):
        """加载所有性能数据"""
        files = ['serial_performance.csv', 'openmp_performance.csv', 'mpi_performance.csv']
        all_data = []
        
        for filename in files:
            data = self.load_csv_file(filename)
            all_data.extend(data)
        
        return all_data
    
    def generate_detailed_report(self, data, output_file="detailed_performance_report.txt"):
        """生成详细的性能分析报告"""
        if not data:
            print("没有数据可供分析")
            return
        
        with open(output_file, 'w', encoding='utf-8') as f:
            # 报告头部
            f.write("=" * 80 + "\n")
            f.write("                矩阵向量乘法并行计算性能分析报告\n")
            f.write("=" * 80 + "\n")
            f.write(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"分析数据条数: {len(data)}\n\n")
            
            # 基本信息
            matrix_size = data[0]['MatrixSize']
            f.write(f"测试配置:\n")
            f.write(f"  矩阵规模: {matrix_size} × {matrix_size}\n")
            f.write(f"  数据类型: 双精度浮点数\n")
            f.write(f"  计算复杂度: O(n²)\n\n")
            
            # 按方法分组分析
            methods = {}
            for row in data:
                method = row['Method']
                if method not in methods:
                    methods[method] = []
                methods[method].append(row)
            
            # 1. 执行时间分析
            f.write("1. 执行时间分析\n")
            f.write("-" * 50 + "\n")
            f.write(f"{'方法':<15} {'最佳时间(s)':<12} {'线程数':<8} {'相对串行':<10}\n")
            f.write("-" * 50 + "\n")
            
            serial_time = None
            best_times = {}
            
            for method, records in methods.items():
                best_record = min(records, key=lambda x: float(x['ExecutionTime']))
                best_time = float(best_record['ExecutionTime'])
                best_times[method] = best_time
                
                if method == 'Serial':
                    serial_time = best_time
                    relative = "1.00x"
                else:
                    relative = f"{serial_time/best_time:.2f}x" if serial_time else "N/A"
                
                f.write(f"{method:<15} {best_time:<12.6f} {best_record['NumThreads']:<8} {relative:<10}\n")
            
            # 2. 加速比分析
            f.write(f"\n2. 加速比与并行效率分析\n")
            f.write("-" * 60 + "\n")
            f.write(f"{'方法':<15} {'线程数':<8} {'加速比':<10} {'效率(%)':<10} {'分类':<12}\n")
            f.write("-" * 60 + "\n")
            
            for method, records in methods.items():
                for record in records:
                    speedup = float(record['Speedup'])
                    efficiency = float(record['Efficiency']) * 100
                    threads = int(record['NumThreads'])
                    
                    # 效率分类
                    if efficiency >= 90:
                        category = "优秀"
                    elif efficiency >= 70:
                        category = "良好"
                    elif efficiency >= 50:
                        category = "一般"
                    else:
                        category = "较差"
                    
                    f.write(f"{method:<15} {threads:<8} {speedup:<10.2f} {efficiency:<10.1f} {category:<12}\n")
            
            # 3. 吞吐量分析
            f.write(f"\n3. 计算吞吐量分析\n")
            f.write("-" * 50 + "\n")
            f.write(f"{'方法':<15} {'线程数':<8} {'吞吐量(GFLOPS)':<15}\n")
            f.write("-" * 50 + "\n")
            
            matrix_size_int = int(matrix_size)
            for method, records in methods.items():
                for record in records:
                    exec_time = float(record['ExecutionTime'])
                    # 矩阵向量乘法的浮点运算次数: 2 * m * n (m=n=matrix_size)
                    flops = 2.0 * matrix_size_int * matrix_size_int
                    throughput = flops / exec_time / 1e9  # GFLOPS
                    threads = record['NumThreads']
                    
                    f.write(f"{method:<15} {threads:<8} {throughput:<15.2f}\n")
            
            # 4. OpenMP可扩展性分析
            if 'OpenMP' in methods:
                f.write(f"\n4. OpenMP可扩展性深度分析\n")
                f.write("-" * 60 + "\n")
                
                openmp_data = sorted(methods['OpenMP'], key=lambda x: int(x['NumThreads']))
                
                f.write(f"{'线程数':<8} {'时间(s)':<10} {'加速比':<10} {'效率(%)':<10} {'可扩展性':<12}\n")
                f.write("-" * 60 + "\n")
                
                base_speedup = float(openmp_data[0]['Speedup']) if openmp_data else 1.0
                
                for i, record in enumerate(openmp_data):
                    threads = int(record['NumThreads'])
                    exec_time = float(record['ExecutionTime'])
                    speedup = float(record['Speedup'])
                    efficiency = float(record['Efficiency']) * 100
                    
                    # 可扩展性评估
                    if i == 0:
                        scalability = "基准"
                    else:
                        expected_speedup = base_speedup * threads / int(openmp_data[0]['NumThreads'])
                        scalability_ratio = speedup / expected_speedup
                        if scalability_ratio >= 0.8:
                            scalability = "良好"
                        elif scalability_ratio >= 0.6:
                            scalability = "一般"
                        else:
                            scalability = "较差"
                    
                    f.write(f"{threads:<8} {exec_time:<10.6f} {speedup:<10.2f} {efficiency:<10.1f} {scalability:<12}\n")
            
            # 5. 性能瓶颈分析
            f.write(f"\n5. 性能瓶颈与优化建议\n")
            f.write("-" * 50 + "\n")
            
            # 分析OpenMP效率下降
            if 'OpenMP' in methods and len(methods['OpenMP']) > 1:
                openmp_sorted = sorted(methods['OpenMP'], key=lambda x: int(x['NumThreads']))
                max_efficiency = max(float(r['Efficiency']) for r in openmp_sorted)
                min_efficiency = min(float(r['Efficiency']) for r in openmp_sorted)
                
                f.write("OpenMP性能分析:\n")
                f.write(f"  最高并行效率: {max_efficiency*100:.1f}%\n")
                f.write(f"  最低并行效率: {min_efficiency*100:.1f}%\n")
                f.write(f"  效率下降幅度: {(max_efficiency-min_efficiency)*100:.1f}%\n")
                
                if min_efficiency < 0.5:
                    f.write("  ⚠️  检测到严重的并行效率下降，可能原因:\n")
                    f.write("     - 线程数超过CPU核心数\n")
                    f.write("     - 内存带宽成为瓶颈\n")
                    f.write("     - False sharing问题\n")
                    f.write("     - 线程创建开销过大\n")
            
            # MPI vs OpenMP对比
            if 'MPI' in methods and 'OpenMP' in methods:
                mpi_best = min(methods['MPI'], key=lambda x: float(x['ExecutionTime']))
                openmp_best = min(methods['OpenMP'], key=lambda x: float(x['ExecutionTime']))
                
                mpi_time = float(mpi_best['ExecutionTime'])
                openmp_time = float(openmp_best['ExecutionTime'])
                
                f.write(f"\nMPI vs OpenMP对比:\n")
                f.write(f"  MPI最佳时间: {mpi_time:.6f}s ({mpi_best['NumThreads']} 进程)\n")
                f.write(f"  OpenMP最佳时间: {openmp_time:.6f}s ({openmp_best['NumThreads']} 线程)\n")
                
                if mpi_time < openmp_time:
                    ratio = openmp_time / mpi_time
                    f.write(f"  MPI性能优势: {ratio:.2f}x\n")
                    f.write("  建议: 对于此规模问题，MPI表现更优\n")
                else:
                    ratio = mpi_time / openmp_time
                    f.write(f"  OpenMP性能优势: {ratio:.2f}x\n")
                    f.write("  建议: 对于此规模问题，OpenMP表现更优\n")
            
            # 6. 总结与建议
            f.write(f"\n6. 总结与优化建议\n")
            f.write("-" * 50 + "\n")
            
            # 找出最佳配置
            all_records = []
            for records in methods.values():
                all_records.extend(records)
            
            best_overall = min(all_records, key=lambda x: float(x['ExecutionTime']))
            
            f.write("最佳性能配置:\n")
            f.write(f"  方法: {best_overall['Method']}\n")
            f.write(f"  线程/进程数: {best_overall['NumThreads']}\n")
            f.write(f"  执行时间: {float(best_overall['ExecutionTime']):.6f} 秒\n")
            f.write(f"  加速比: {float(best_overall['Speedup']):.2f}\n")
            f.write(f"  并行效率: {float(best_overall['Efficiency'])*100:.1f}%\n")
            
            f.write(f"\n通用优化建议:\n")
            f.write("1. 矩阵存储优化: 使用行主序存储，提高缓存命中率\n")
            f.write("2. 内存对齐: 确保数据按缓存行大小对齐\n")
            f.write("3. SIMD优化: 利用向量化指令加速内层循环\n")
            f.write("4. 负载均衡: 确保各线程工作量均匀分配\n")
            f.write("5. 内存带宽: 对于大矩阵，考虑内存带宽限制\n")
            f.write("6. 算法优化: 考虑分块算法减少缓存缺失\n")
            
            f.write(f"\n" + "=" * 80 + "\n")
            f.write("报告结束\n")
            f.write("=" * 80 + "\n")
        
        print(f"详细报告已生成: {output_file}")

def main():
    print("简化版性能报告生成器")
    print("=" * 40)
    
    generator = SimpleReportGenerator()
    data = generator.load_all_data()
    
    if not data:
        print("没有找到性能数据文件")
        return
    
    print(f"总共加载了 {len(data)} 条性能记录")
    
    # 生成详细报告
    generator.generate_detailed_report(data)
    
    print("报告生成完成！")

if __name__ == "__main__":
    main()
