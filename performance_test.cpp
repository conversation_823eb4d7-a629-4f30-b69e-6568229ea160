#include "utils.h"
#include <omp.h>
#include <iostream>
#include <iomanip>
#include <fstream>
#include <algorithm>
#include <sstream>
#include <map>
#include <numeric>

/**
 * 综合性能测试程序
 * 对比串行、OpenMP和MPI的性能表现
 */
class PerformanceAnalyzer {
private:
    std::vector<PerformanceResult> all_results;
    
public:
    void loadResults(const std::string& filename) {
        std::ifstream file(filename);
        if (!file.is_open()) {
            std::cout << "警告: 无法打开文件 " << filename << std::endl;
            return;
        }
        
        std::string line;
        std::getline(file, line); // 跳过标题行
        
        while (std::getline(file, line)) {
            if (line.empty()) continue;
            
            PerformanceResult result;
            std::stringstream ss(line);
            std::string item;
            
            // 解析CSV行
            std::getline(ss, result.method, ',');
            std::getline(ss, item, ',');
            result.matrix_size = std::stoi(item);
            std::getline(ss, item, ',');
            result.num_threads = std::stoi(item);
            std::getline(ss, item, ',');
            result.execution_time = std::stod(item);
            std::getline(ss, item, ',');
            result.speedup = std::stod(item);
            std::getline(ss, item, ',');
            result.efficiency = std::stod(item);
            
            all_results.push_back(result);
        }
        file.close();
    }
    
    void generateReport() {
        if (all_results.empty()) {
            std::cout << "没有性能数据可供分析" << std::endl;
            return;
        }
        
        std::cout << "\n" << std::string(80, '=') << std::endl;
        std::cout << "                    矩阵向量乘法并行性能分析报告" << std::endl;
        std::cout << std::string(80, '=') << std::endl;
        
        // 按方法分组分析
        analyzeByMethod();
        
        // 可扩展性分析
        analyzeScalability();
        
        // 效率分析
        analyzeEfficiency();
        
        // 最佳性能推荐
        recommendOptimal();
    }
    
private:
    void analyzeByMethod() {
        std::cout << "\n1. 各方法性能对比" << std::endl;
        std::cout << std::string(60, '-') << std::endl;
        
        std::map<std::string, std::vector<PerformanceResult>> method_groups;
        for (const auto& result : all_results) {
            method_groups[result.method].push_back(result);
        }
        
        std::cout << std::setw(15) << "方法" << std::setw(12) << "最佳时间(s)" 
                  << std::setw(10) << "最大加速比" << std::setw(12) << "最高效率" 
                  << std::setw(10) << "线程数" << std::endl;
        std::cout << std::string(60, '-') << std::endl;
        
        for (const auto& group : method_groups) {
            const auto& results = group.second;
            auto best_time = std::min_element(results.begin(), results.end(),
                [](const PerformanceResult& a, const PerformanceResult& b) {
                    return a.execution_time < b.execution_time;
                });
            
            auto best_speedup = std::max_element(results.begin(), results.end(),
                [](const PerformanceResult& a, const PerformanceResult& b) {
                    return a.speedup < b.speedup;
                });
            
            auto best_efficiency = std::max_element(results.begin(), results.end(),
                [](const PerformanceResult& a, const PerformanceResult& b) {
                    return a.efficiency < b.efficiency;
                });
            
            std::cout << std::fixed << std::setprecision(4);
            std::cout << std::setw(15) << group.first
                      << std::setw(12) << best_time->execution_time
                      << std::setw(10) << best_speedup->speedup
                      << std::setw(12) << best_efficiency->efficiency * 100 << "%"
                      << std::setw(10) << best_speedup->num_threads << std::endl;
        }
    }
    
    void analyzeScalability() {
        std::cout << "\n2. 可扩展性分析" << std::endl;
        std::cout << std::string(60, '-') << std::endl;
        
        // 分析OpenMP的可扩展性
        std::vector<PerformanceResult> openmp_results;
        for (const auto& result : all_results) {
            if (result.method == "OpenMP") {
                openmp_results.push_back(result);
            }
        }
        
        if (!openmp_results.empty()) {
            std::sort(openmp_results.begin(), openmp_results.end(),
                [](const PerformanceResult& a, const PerformanceResult& b) {
                    return a.num_threads < b.num_threads;
                });
            
            std::cout << "OpenMP可扩展性:" << std::endl;
            std::cout << std::setw(8) << "线程数" << std::setw(12) << "执行时间(s)" 
                      << std::setw(10) << "加速比" << std::setw(12) << "效率" << std::endl;
            
            for (const auto& result : openmp_results) {
                std::cout << std::fixed << std::setprecision(4);
                std::cout << std::setw(8) << result.num_threads
                          << std::setw(12) << result.execution_time
                          << std::setw(10) << result.speedup
                          << std::setw(12) << result.efficiency * 100 << "%" << std::endl;
            }
            
            // 计算可扩展性指标
            if (openmp_results.size() >= 2) {
                double scalability = openmp_results.back().speedup / openmp_results.front().speedup;
                double thread_ratio = static_cast<double>(openmp_results.back().num_threads) / 
                                    openmp_results.front().num_threads;
                double scalability_efficiency = scalability / thread_ratio;
                
                std::cout << "\n可扩展性指标:" << std::endl;
                std::cout << "  实际加速比提升: " << scalability << "倍" << std::endl;
                std::cout << "  理论加速比提升: " << thread_ratio << "倍" << std::endl;
                std::cout << "  可扩展性效率: " << scalability_efficiency * 100 << "%" << std::endl;
            }
        }
    }
    
    void analyzeEfficiency() {
        std::cout << "\n3. 并行效率分析" << std::endl;
        std::cout << std::string(60, '-') << std::endl;
        
        // 计算各方法的平均效率
        std::map<std::string, std::vector<double>> efficiency_map;
        for (const auto& result : all_results) {
            if (result.efficiency > 0) {
                efficiency_map[result.method].push_back(result.efficiency);
            }
        }
        
        std::cout << std::setw(15) << "方法" << std::setw(12) << "平均效率" 
                  << std::setw(12) << "最高效率" << std::setw(12) << "最低效率" << std::endl;
        std::cout << std::string(50, '-') << std::endl;
        
        for (const auto& group : efficiency_map) {
            const auto& efficiencies = group.second;
            double avg_eff = std::accumulate(efficiencies.begin(), efficiencies.end(), 0.0) / efficiencies.size();
            double max_eff = *std::max_element(efficiencies.begin(), efficiencies.end());
            double min_eff = *std::min_element(efficiencies.begin(), efficiencies.end());
            
            std::cout << std::fixed << std::setprecision(2);
            std::cout << std::setw(15) << group.first
                      << std::setw(12) << avg_eff * 100 << "%"
                      << std::setw(12) << max_eff * 100 << "%"
                      << std::setw(12) << min_eff * 100 << "%" << std::endl;
        }
    }
    
    void recommendOptimal() {
        std::cout << "\n4. 性能优化建议" << std::endl;
        std::cout << std::string(60, '-') << std::endl;
        
        // 找到最佳性能配置
        auto best_overall = std::min_element(all_results.begin(), all_results.end(),
            [](const PerformanceResult& a, const PerformanceResult& b) {
                return a.execution_time < b.execution_time;
            });
        
        if (best_overall != all_results.end()) {
            std::cout << "最佳性能配置:" << std::endl;
            std::cout << "  方法: " << best_overall->method << std::endl;
            std::cout << "  线程/进程数: " << best_overall->num_threads << std::endl;
            std::cout << "  执行时间: " << best_overall->execution_time << " 秒" << std::endl;
            std::cout << "  加速比: " << best_overall->speedup << std::endl;
            std::cout << "  并行效率: " << best_overall->efficiency * 100 << "%" << std::endl;
        }
        
        // 效率最高的配置
        auto best_efficiency = std::max_element(all_results.begin(), all_results.end(),
            [](const PerformanceResult& a, const PerformanceResult& b) {
                return a.efficiency < b.efficiency;
            });
        
        if (best_efficiency != all_results.end() && best_efficiency->efficiency > 0) {
            std::cout << "\n最高效率配置:" << std::endl;
            std::cout << "  方法: " << best_efficiency->method << std::endl;
            std::cout << "  线程/进程数: " << best_efficiency->num_threads << std::endl;
            std::cout << "  并行效率: " << best_efficiency->efficiency * 100 << "%" << std::endl;
            std::cout << "  执行时间: " << best_efficiency->execution_time << " 秒" << std::endl;
        }
        
        // 给出建议
        std::cout << "\n优化建议:" << std::endl;
        std::cout << "1. 对于计算密集型任务，推荐使用OpenMP并行化" << std::endl;
        std::cout << "2. 对于大规模分布式计算，考虑使用MPI" << std::endl;
        std::cout << "3. 线程数不宜超过CPU核心数，避免过度订阅" << std::endl;
        std::cout << "4. 注意内存访问模式，减少false sharing" << std::endl;
        std::cout << "5. 考虑使用SIMD指令进一步优化内层循环" << std::endl;
    }
};

int main(int argc, char* argv[]) {
    std::cout << "=== 矩阵向量乘法性能测试与分析 ===" << std::endl;
    
    int matrix_size = DEFAULT_MATRIX_SIZE;
    if (argc > 1) {
        matrix_size = std::atoi(argv[1]);
    }
    
    std::cout << "测试矩阵大小: " << matrix_size << "x" << matrix_size << std::endl;
    
    try {
        // 检查是否存在测试数据，如果不存在则生成
        std::ifstream test_file("test_matrix.dat");
        if (!test_file.is_open()) {
            std::cout << "生成测试数据..." << std::endl;
            Matrix matrix = MatrixUtils::generateRandomMatrix(matrix_size, matrix_size, 0.0, 1.0);
            Vector vector = MatrixUtils::generateRandomVector(matrix_size, 0.0, 1.0);
            MatrixUtils::saveMatrixToFile(matrix, "test_matrix.dat");
            MatrixUtils::saveVectorToFile(vector, "test_vector.dat");
        } else {
            test_file.close();
        }
        
        // 创建性能分析器
        PerformanceAnalyzer analyzer;
        
        // 加载所有性能结果
        analyzer.loadResults("serial_performance.csv");
        analyzer.loadResults("openmp_performance.csv");
        analyzer.loadResults("mpi_performance.csv");
        
        // 生成综合分析报告
        analyzer.generateReport();
        
        std::cout << "\n" << std::string(80, '=') << std::endl;
        std::cout << "性能分析完成！" << std::endl;
        std::cout << "详细结果请查看各个CSV文件。" << std::endl;
        std::cout << std::string(80, '=') << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "错误: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
