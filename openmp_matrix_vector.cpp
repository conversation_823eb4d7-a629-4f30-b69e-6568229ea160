#include "utils.h"
#include <omp.h>
#include <iostream>
#include <iomanip>
#include <fstream>

/**
 * OpenMP并行矩阵向量乘法实现
 * 使用循环并行化进行加速
 */
class OpenMPMatrixVector {
public:
    static Vector multiply(const Matrix& matrix, const Vector& vector, int num_threads = 0) {
        int rows = matrix.size();
        int cols = matrix[0].size();
        
        if (cols != static_cast<int>(vector.size())) {
            throw std::invalid_argument("Matrix columns must equal vector size");
        }
        
        Vector result(rows, 0.0);
        
        // 设置线程数
        if (num_threads > 0) {
            omp_set_num_threads(num_threads);
        }
        
        // OpenMP并行计算矩阵向量乘法
        #pragma omp parallel for
        for (int i = 0; i < rows; ++i) {
            double sum = 0.0;
            for (int j = 0; j < cols; ++j) {
                sum += matrix[i][j] * vector[j];
            }
            result[i] = sum;
        }
        
        return result;
    }
    
    static Vector multiplyOptimized(const Matrix& matrix, const Vector& vector, int num_threads = 0) {
        int rows = matrix.size();
        int cols = matrix[0].size();
        
        if (cols != static_cast<int>(vector.size())) {
            throw std::invalid_argument("Matrix columns must equal vector size");
        }
        
        Vector result(rows, 0.0);
        
        // 设置线程数
        if (num_threads > 0) {
            omp_set_num_threads(num_threads);
        }
        
        // 优化版本：使用reduction减少false sharing
        #pragma omp parallel for schedule(static)
        for (int i = 0; i < rows; ++i) {
            double local_sum = 0.0;
            #pragma omp simd reduction(+:local_sum)
            for (int j = 0; j < cols; ++j) {
                local_sum += matrix[i][j] * vector[j];
            }
            result[i] = local_sum;
        }
        
        return result;
    }
    
    static double benchmark(const Matrix& matrix, const Vector& vector, 
                          int num_threads = 0, int iterations = 1, bool optimized = false) {
        Timer timer;
        Vector result;
        
        timer.start();
        for (int iter = 0; iter < iterations; ++iter) {
            if (optimized) {
                result = multiplyOptimized(matrix, vector, num_threads);
            } else {
                result = multiply(matrix, vector, num_threads);
            }
        }
        double total_time = timer.stop();
        
        return total_time / iterations;
    }
};

int main(int argc, char* argv[]) {
    std::cout << "=== OpenMP并行矩阵向量乘法测试 ===" << std::endl;
    
    // 解析命令行参数
    int max_threads = omp_get_max_threads();
    int iterations = 5;
    bool test_optimized = true;
    
    if (argc > 1) {
        max_threads = std::atoi(argv[1]);
    }
    if (argc > 2) {
        iterations = std::atoi(argv[2]);
    }
    
    std::cout << "最大线程数: " << max_threads << std::endl;
    std::cout << "迭代次数: " << iterations << std::endl;
    
    try {
        // 读取测试数据
        std::cout << "\n正在读取测试数据..." << std::endl;
        Matrix matrix = MatrixUtils::loadMatrixFromFile("test_matrix.dat");
        Vector vector = MatrixUtils::loadVectorFromFile("test_vector.dat");
        
        int rows = matrix.size();
        int cols = matrix[0].size();
        
        std::cout << "矩阵大小: " << rows << "x" << cols << std::endl;
        
        // 读取串行基准时间
        double serial_time = 0.0;
        std::ifstream serial_file("serial_performance.csv");
        if (serial_file.is_open()) {
            std::string line;
            std::getline(serial_file, line); // 跳过标题行
            if (std::getline(serial_file, line)) {
                size_t pos = line.find_last_of(',');
                if (pos != std::string::npos) {
                    pos = line.find_last_of(',', pos - 1);
                    if (pos != std::string::npos) {
                        pos = line.find_last_of(',', pos - 1);
                        if (pos != std::string::npos) {
                            serial_time = std::stod(line.substr(pos + 1, 
                                line.find(',', pos + 1) - pos - 1));
                        }
                    }
                }
            }
            serial_file.close();
        }
        
        // 加载串行结果用于验证
        Vector serial_result = MatrixUtils::loadVectorFromFile("serial_result.dat");
        
        std::vector<PerformanceResult> results;
        
        // 测试不同线程数的性能
        std::cout << "\n=== 性能测试 ===" << std::endl;
        std::cout << std::setw(8) << "线程数" << std::setw(12) << "执行时间(s)" 
                  << std::setw(10) << "加速比" << std::setw(12) << "并行效率" 
                  << std::setw(12) << "吞吐量(GFLOPS)" << std::setw(10) << "正确性" << std::endl;
        std::cout << std::string(74, '-') << std::endl;
        
        for (int threads = 1; threads <= max_threads; threads *= 2) {
            // 基础版本测试
            double avg_time = OpenMPMatrixVector::benchmark(matrix, vector, threads, iterations, false);
            Vector result = OpenMPMatrixVector::multiply(matrix, vector, threads);
            
            // 验证结果
            bool is_correct = MatrixUtils::compareVectors(result, serial_result, TOLERANCE);
            double error = MatrixUtils::calculateError(result, serial_result);
            
            // 计算性能指标
            double speedup = (serial_time > 0) ? serial_time / avg_time : 0.0;
            double efficiency = speedup / threads;
            double throughput = (2.0 * rows * cols) / avg_time / 1e9;
            
            // 显示结果
            std::cout << std::fixed << std::setprecision(4);
            std::cout << std::setw(8) << threads 
                      << std::setw(12) << avg_time
                      << std::setw(10) << speedup
                      << std::setw(12) << efficiency * 100 << "%"
                      << std::setw(12) << throughput
                      << std::setw(10) << (is_correct ? "正确" : "错误") << std::endl;
            
            // 保存性能结果
            PerformanceResult perf_result;
            perf_result.method = "OpenMP";
            perf_result.matrix_size = rows;
            perf_result.num_threads = threads;
            perf_result.execution_time = avg_time;
            perf_result.speedup = speedup;
            perf_result.efficiency = efficiency;
            results.push_back(perf_result);
            
            // 保存特定线程数的结果
            if (threads == max_threads) {
                MatrixUtils::saveVectorToFile(result, "openmp_result.dat");
            }
        }
        
        // 测试优化版本（使用最大线程数）
        if (test_optimized) {
            std::cout << "\n=== 优化版本测试 ===" << std::endl;
            double opt_time = OpenMPMatrixVector::benchmark(matrix, vector, max_threads, iterations, true);
            Vector opt_result = OpenMPMatrixVector::multiplyOptimized(matrix, vector, max_threads);
            
            bool opt_correct = MatrixUtils::compareVectors(opt_result, serial_result, TOLERANCE);
            double opt_speedup = (serial_time > 0) ? serial_time / opt_time : 0.0;
            double opt_efficiency = opt_speedup / max_threads;
            double opt_throughput = (2.0 * rows * cols) / opt_time / 1e9;
            
            std::cout << "优化版本 (" << max_threads << " 线程):" << std::endl;
            std::cout << "  执行时间: " << opt_time << " 秒" << std::endl;
            std::cout << "  加速比: " << opt_speedup << std::endl;
            std::cout << "  并行效率: " << opt_efficiency * 100 << "%" << std::endl;
            std::cout << "  吞吐量: " << opt_throughput << " GFLOPS" << std::endl;
            std::cout << "  正确性: " << (opt_correct ? "正确" : "错误") << std::endl;
            
            // 添加优化版本结果
            PerformanceResult opt_perf_result;
            opt_perf_result.method = "OpenMP_Optimized";
            opt_perf_result.matrix_size = rows;
            opt_perf_result.num_threads = max_threads;
            opt_perf_result.execution_time = opt_time;
            opt_perf_result.speedup = opt_speedup;
            opt_perf_result.efficiency = opt_efficiency;
            results.push_back(opt_perf_result);
            
            MatrixUtils::saveVectorToFile(opt_result, "openmp_optimized_result.dat");
        }
        
        // 保存所有性能结果
        MatrixUtils::savePerformanceResults(results, "openmp_performance.csv");
        
        std::cout << "\nOpenMP并行计算完成！结果已保存到文件。" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "错误: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
