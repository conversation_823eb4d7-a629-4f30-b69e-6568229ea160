#!/usr/bin/env python3
"""
矩阵向量乘法性能可视化脚本
生成性能对比图表和分析报告
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
from pathlib import Path
import sys

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['Times New Roman', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class PerformanceVisualizer:
    def __init__(self):
        self.results = []
        self.colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']
        
    def load_data(self, filename):
        """加载性能数据"""
        try:
            df = pd.read_csv(filename)
            self.results.append(df)
            print(f"成功加载数据文件: {filename}")
            return df
        except FileNotFoundError:
            print(f"警告: 文件 {filename} 不存在")
            return None
        except Exception as e:
            print(f"错误: 加载文件 {filename} 失败 - {e}")
            return None
    
    def combine_data(self):
        """合并所有数据"""
        if not self.results:
            print("没有可用的数据")
            return None
        
        combined = pd.concat(self.results, ignore_index=True)
        return combined
    
    def plot_execution_time_comparison(self, data, save_path="execution_time_comparison.png"):
        """绘制执行时间对比图"""
        plt.figure(figsize=(12, 8))
        
        # 按方法分组
        methods = data['Method'].unique()
        x_pos = np.arange(len(methods))
        
        # 获取每种方法的最佳执行时间
        best_times = []
        thread_counts = []
        
        for method in methods:
            method_data = data[data['Method'] == method]
            best_idx = method_data['ExecutionTime'].idxmin()
            best_times.append(method_data.loc[best_idx, 'ExecutionTime'])
            thread_counts.append(method_data.loc[best_idx, 'NumThreads'])
        
        bars = plt.bar(x_pos, best_times, color=self.colors[:len(methods)], alpha=0.8)
        
        # 添加数值标签
        for i, (bar, time, threads) in enumerate(zip(bars, best_times, thread_counts)):
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                    f'{time:.4f}s\n({threads} 线程)', 
                    ha='center', va='bottom', fontsize=10)
        
        plt.xlabel('并行化方法', fontsize=12)
        plt.ylabel('执行时间 (秒)', fontsize=12)
        plt.title('不同并行化方法的最佳执行时间对比', fontsize=14, fontweight='bold')
        plt.xticks(x_pos, methods)
        plt.grid(axis='y', alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        print(f"执行时间对比图已保存: {save_path}")
    
    def plot_speedup_analysis(self, data, save_path="speedup_analysis.png"):
        """绘制加速比分析图"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
        
        # 左图: 加速比随线程数变化
        openmp_data = data[data['Method'] == 'OpenMP'].copy()
        if not openmp_data.empty:
            openmp_data = openmp_data.sort_values('NumThreads')
            
            ax1.plot(openmp_data['NumThreads'], openmp_data['Speedup'], 
                    'o-', linewidth=2, markersize=8, color=self.colors[1], label='实际加速比')
            
            # 理想加速比线
            max_threads = openmp_data['NumThreads'].max()
            ideal_threads = range(1, max_threads + 1)
            ax1.plot(ideal_threads, ideal_threads, '--', 
                    color='gray', alpha=0.7, label='理想加速比')
            
            ax1.set_xlabel('线程数', fontsize=12)
            ax1.set_ylabel('加速比', fontsize=12)
            ax1.set_title('OpenMP加速比分析', fontsize=14, fontweight='bold')
            ax1.grid(True, alpha=0.3)
            ax1.legend()
            ax1.set_xlim(0.5, max_threads + 0.5)
        
        # 右图: 并行效率
        if not openmp_data.empty:
            ax2.plot(openmp_data['NumThreads'], openmp_data['Efficiency'] * 100, 
                    's-', linewidth=2, markersize=8, color=self.colors[2], label='并行效率')
            
            # 添加效率阈值线
            ax2.axhline(y=80, color='orange', linestyle='--', alpha=0.7, label='80%效率线')
            ax2.axhline(y=50, color='red', linestyle='--', alpha=0.7, label='50%效率线')
            
            ax2.set_xlabel('线程数', fontsize=12)
            ax2.set_ylabel('并行效率 (%)', fontsize=12)
            ax2.set_title('OpenMP并行效率分析', fontsize=14, fontweight='bold')
            ax2.grid(True, alpha=0.3)
            ax2.legend()
            ax2.set_xlim(0.5, max_threads + 0.5)
            ax2.set_ylim(0, 105)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        print(f"加速比分析图已保存: {save_path}")
    
    def plot_throughput_comparison(self, data, save_path="throughput_comparison.png"):
        """绘制吞吐量对比图"""
        plt.figure(figsize=(12, 8))
        
        # 计算吞吐量 (GFLOPS)
        matrix_size = data['MatrixSize'].iloc[0]
        data_copy = data.copy()
        data_copy['Throughput'] = (2.0 * matrix_size * matrix_size) / data_copy['ExecutionTime'] / 1e9
        
        # 按方法和线程数绘制
        methods = data_copy['Method'].unique()
        
        for i, method in enumerate(methods):
            method_data = data_copy[data_copy['Method'] == method]
            if method == 'OpenMP':
                # OpenMP显示不同线程数的吞吐量
                method_data = method_data.sort_values('NumThreads')
                plt.plot(method_data['NumThreads'], method_data['Throughput'], 
                        'o-', linewidth=2, markersize=8, color=self.colors[i], label=method)
            else:
                # 其他方法显示单点
                best_idx = method_data['Throughput'].idxmax()
                best_throughput = method_data.loc[best_idx, 'Throughput']
                best_threads = method_data.loc[best_idx, 'NumThreads']
                plt.scatter(best_threads, best_throughput, s=100, 
                          color=self.colors[i], label=f'{method} (最佳)', zorder=5)
        
        plt.xlabel('线程/进程数', fontsize=12)
        plt.ylabel('计算吞吐量 (GFLOPS)', fontsize=12)
        plt.title('不同并行化方法的计算吞吐量对比', fontsize=14, fontweight='bold')
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        print(f"吞吐量对比图已保存: {save_path}")
    
    def plot_efficiency_heatmap(self, data, save_path="efficiency_heatmap.png"):
        """绘制效率热力图"""
        openmp_data = data[data['Method'] == 'OpenMP'].copy()
        if openmp_data.empty:
            print("没有OpenMP数据，跳过效率热力图")
            return
        
        # 创建效率矩阵
        threads = sorted(openmp_data['NumThreads'].unique())
        matrix_sizes = sorted(openmp_data['MatrixSize'].unique())
        
        if len(matrix_sizes) == 1:
            # 如果只有一个矩阵大小，创建一个简单的条形图
            plt.figure(figsize=(10, 6))
            efficiencies = [openmp_data[openmp_data['NumThreads'] == t]['Efficiency'].iloc[0] * 100 
                          for t in threads]
            
            bars = plt.bar(range(len(threads)), efficiencies, color=self.colors[1], alpha=0.8)
            
            # 添加数值标签
            for bar, eff in zip(bars, efficiencies):
                height = bar.get_height()
                plt.text(bar.get_x() + bar.get_width()/2., height + 1,
                        f'{eff:.1f}%', ha='center', va='bottom')
            
            plt.xlabel('线程数', fontsize=12)
            plt.ylabel('并行效率 (%)', fontsize=12)
            plt.title('OpenMP并行效率分布', fontsize=14, fontweight='bold')
            plt.xticks(range(len(threads)), threads)
            plt.grid(axis='y', alpha=0.3)
            plt.ylim(0, 105)
        else:
            # 多个矩阵大小的热力图
            efficiency_matrix = np.zeros((len(matrix_sizes), len(threads)))
            
            for i, size in enumerate(matrix_sizes):
                for j, thread in enumerate(threads):
                    subset = openmp_data[(openmp_data['MatrixSize'] == size) & 
                                       (openmp_data['NumThreads'] == thread)]
                    if not subset.empty:
                        efficiency_matrix[i, j] = subset['Efficiency'].iloc[0] * 100
            
            plt.figure(figsize=(12, 8))
            sns.heatmap(efficiency_matrix, 
                       xticklabels=threads, 
                       yticklabels=matrix_sizes,
                       annot=True, 
                       fmt='.1f', 
                       cmap='RdYlGn', 
                       vmin=0, 
                       vmax=100,
                       cbar_kws={'label': '并行效率 (%)'})
            
            plt.xlabel('线程数', fontsize=12)
            plt.ylabel('矩阵大小', fontsize=12)
            plt.title('OpenMP并行效率热力图', fontsize=14, fontweight='bold')
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        print(f"效率热力图已保存: {save_path}")
    
    def generate_summary_report(self, data, save_path="performance_summary.txt"):
        """生成性能总结报告"""
        with open(save_path, 'w', encoding='utf-8') as f:
            f.write("矩阵向量乘法并行性能分析报告\n")
            f.write("=" * 50 + "\n\n")
            
            # 基本信息
            matrix_size = data['MatrixSize'].iloc[0]
            f.write(f"测试矩阵大小: {matrix_size} x {matrix_size}\n")
            f.write(f"测试方法数量: {len(data['Method'].unique())}\n")
            f.write(f"总测试配置: {len(data)}\n\n")
            
            # 各方法最佳性能
            f.write("各方法最佳性能:\n")
            f.write("-" * 30 + "\n")
            
            for method in data['Method'].unique():
                method_data = data[data['Method'] == method]
                best_idx = method_data['ExecutionTime'].idxmin()
                best_result = method_data.loc[best_idx]
                
                f.write(f"{method}:\n")
                f.write(f"  最佳执行时间: {best_result['ExecutionTime']:.6f} 秒\n")
                f.write(f"  使用线程数: {best_result['NumThreads']}\n")
                f.write(f"  加速比: {best_result['Speedup']:.2f}\n")
                f.write(f"  并行效率: {best_result['Efficiency']*100:.1f}%\n")
                
                # 计算吞吐量
                throughput = (2.0 * matrix_size * matrix_size) / best_result['ExecutionTime'] / 1e9
                f.write(f"  计算吞吐量: {throughput:.2f} GFLOPS\n\n")
            
            # 性能排名
            f.write("性能排名 (按执行时间):\n")
            f.write("-" * 30 + "\n")
            
            best_configs = []
            for method in data['Method'].unique():
                method_data = data[data['Method'] == method]
                best_idx = method_data['ExecutionTime'].idxmin()
                best_configs.append(method_data.loc[best_idx])
            
            best_configs.sort(key=lambda x: x['ExecutionTime'])
            
            for i, config in enumerate(best_configs, 1):
                f.write(f"{i}. {config['Method']} ({config['NumThreads']} 线程): "
                       f"{config['ExecutionTime']:.6f} 秒\n")
            
            f.write(f"\n报告生成时间: {pd.Timestamp.now()}\n")
        
        print(f"性能总结报告已保存: {save_path}")

def main():
    print("矩阵向量乘法性能可视化工具")
    print("=" * 40)
    
    visualizer = PerformanceVisualizer()
    
    # 加载数据文件
    data_files = ['serial_performance.csv', 'openmp_performance.csv', 'mpi_performance.csv']
    
    for filename in data_files:
        visualizer.load_data(filename)
    
    # 合并数据
    combined_data = visualizer.combine_data()
    
    if combined_data is None or combined_data.empty:
        print("没有可用的性能数据，请先运行性能测试")
        return
    
    print(f"\n成功加载 {len(combined_data)} 条性能记录")
    print("正在生成可视化图表...")
    
    # 生成各种图表
    visualizer.plot_execution_time_comparison(combined_data)
    visualizer.plot_speedup_analysis(combined_data)
    visualizer.plot_throughput_comparison(combined_data)
    visualizer.plot_efficiency_heatmap(combined_data)
    
    # 生成总结报告
    visualizer.generate_summary_report(combined_data)
    
    print("\n所有可视化图表和报告已生成完成！")

if __name__ == "__main__":
    main()
