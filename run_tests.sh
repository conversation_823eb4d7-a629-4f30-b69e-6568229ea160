#!/bin/bash

# 矩阵向量乘法并行性能测试脚本
# 自动运行所有测试并生成完整的性能分析报告

echo "=========================================="
echo "    矩阵向量乘法并行性能测试套件"
echo "=========================================="

# 设置默认参数
MATRIX_SIZE=1000
ITERATIONS=5
MAX_THREADS=8
MPI_PROCESSES=4

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -s|--size)
            MATRIX_SIZE="$2"
            shift 2
            ;;
        -i|--iterations)
            ITERATIONS="$2"
            shift 2
            ;;
        -t|--threads)
            MAX_THREADS="$2"
            shift 2
            ;;
        -p|--processes)
            MPI_PROCESSES="$2"
            shift 2
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  -s, --size SIZE        矩阵大小 (默认: 1000)"
            echo "  -i, --iterations N     迭代次数 (默认: 5)"
            echo "  -t, --threads N        最大线程数 (默认: 8)"
            echo "  -p, --processes N      MPI进程数 (默认: 4)"
            echo "  -h, --help            显示此帮助信息"
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            echo "使用 -h 或 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

echo "测试参数:"
echo "  矩阵大小: ${MATRIX_SIZE}x${MATRIX_SIZE}"
echo "  迭代次数: ${ITERATIONS}"
echo "  最大线程数: ${MAX_THREADS}"
echo "  MPI进程数: ${MPI_PROCESSES}"
echo ""

# 检查编译依赖
echo "检查编译环境..."
check_command() {
    if ! command -v $1 &> /dev/null; then
        echo "错误: 未找到 $1 命令"
        return 1
    fi
    return 0
}

check_command "g++" || exit 1
check_command "make" || exit 1

# 检查MPI
if command -v mpirun &> /dev/null && command -v mpic++ &> /dev/null; then
    MPI_AVAILABLE=true
    echo "✓ MPI 环境可用"
else
    MPI_AVAILABLE=false
    echo "⚠ MPI 环境不可用，将跳过MPI测试"
fi

# 检查Python和必要的库
if command -v python3 &> /dev/null; then
    PYTHON_AVAILABLE=true
    echo "✓ Python3 可用"
    
    # 检查必要的Python库
    python3 -c "import matplotlib, pandas, numpy, seaborn" 2>/dev/null
    if [ $? -eq 0 ]; then
        VISUALIZATION_AVAILABLE=true
        echo "✓ 可视化库可用"
    else
        VISUALIZATION_AVAILABLE=false
        echo "⚠ 可视化库不完整，将跳过图表生成"
        echo "  请安装: pip3 install matplotlib pandas numpy seaborn"
    fi
else
    PYTHON_AVAILABLE=false
    VISUALIZATION_AVAILABLE=false
    echo "⚠ Python3 不可用，将跳过可视化"
fi

echo ""

# 清理旧文件
echo "清理旧的测试文件..."
make clean > /dev/null 2>&1

# 编译所有程序
echo "编译程序..."
if ! make all; then
    echo "编译失败！"
    exit 1
fi
echo "✓ 编译完成"
echo ""

# 运行测试
echo "=========================================="
echo "开始性能测试"
echo "=========================================="

# 1. 串行测试
echo "1. 运行串行基准测试..."
if ./serial_matrix_vector ${MATRIX_SIZE} ${ITERATIONS}; then
    echo "✓ 串行测试完成"
else
    echo "✗ 串行测试失败"
    exit 1
fi
echo ""

# 2. OpenMP测试
echo "2. 运行OpenMP并行测试..."
if ./openmp_matrix_vector ${MAX_THREADS} ${ITERATIONS}; then
    echo "✓ OpenMP测试完成"
else
    echo "✗ OpenMP测试失败"
    exit 1
fi
echo ""

# 3. MPI测试
if [ "$MPI_AVAILABLE" = true ]; then
    echo "3. 运行MPI并行测试..."
    if mpirun -np ${MPI_PROCESSES} ./mpi_matrix_vector; then
        echo "✓ MPI测试完成"
    else
        echo "⚠ MPI测试失败，但继续执行"
    fi
else
    echo "3. 跳过MPI测试 (MPI不可用)"
fi
echo ""

# 4. 性能分析
echo "4. 运行性能分析..."
if ./performance_test ${MATRIX_SIZE}; then
    echo "✓ 性能分析完成"
else
    echo "⚠ 性能分析失败，但继续执行"
fi
echo ""

# 5. 生成可视化报告
if [ "$VISUALIZATION_AVAILABLE" = true ]; then
    echo "5. 生成可视化报告..."
    if python3 visualize_results.py; then
        echo "✓ 可视化报告生成完成"
    else
        echo "⚠ 可视化报告生成失败"
    fi
else
    echo "5. 跳过可视化报告生成 (Python库不可用)"
fi
echo ""

# 显示结果文件
echo "=========================================="
echo "测试完成！生成的文件:"
echo "=========================================="

echo "数据文件:"
for file in *.dat; do
    if [ -f "$file" ]; then
        echo "  $file"
    fi
done

echo ""
echo "性能结果:"
for file in *_performance.csv; do
    if [ -f "$file" ]; then
        echo "  $file"
    fi
done

echo ""
echo "可视化图表:"
for file in *.png; do
    if [ -f "$file" ]; then
        echo "  $file"
    fi
done

echo ""
echo "报告文件:"
for file in *.txt; do
    if [ -f "$file" ]; then
        echo "  $file"
    fi
done

echo ""
echo "=========================================="
echo "性能测试套件执行完成！"
echo "=========================================="

# 显示快速性能摘要
if [ -f "serial_performance.csv" ] && [ -f "openmp_performance.csv" ]; then
    echo ""
    echo "快速性能摘要:"
    echo "=============="
    
    # 提取串行时间
    serial_time=$(tail -n 1 serial_performance.csv | cut -d',' -f4)
    
    # 提取OpenMP最佳时间
    if [ -f "openmp_performance.csv" ]; then
        openmp_best=$(tail -n +2 openmp_performance.csv | cut -d',' -f4 | sort -n | head -n 1)
        if [ ! -z "$openmp_best" ] && [ ! -z "$serial_time" ]; then
            speedup=$(echo "scale=2; $serial_time / $openmp_best" | bc -l 2>/dev/null || echo "N/A")
            echo "串行时间: ${serial_time} 秒"
            echo "OpenMP最佳时间: ${openmp_best} 秒"
            echo "最大加速比: ${speedup}x"
        fi
    fi
    
    # 提取MPI时间
    if [ -f "mpi_performance.csv" ]; then
        mpi_time=$(tail -n 1 mpi_performance.csv | cut -d',' -f4)
        if [ ! -z "$mpi_time" ] && [ ! -z "$serial_time" ]; then
            mpi_speedup=$(echo "scale=2; $serial_time / $mpi_time" | bc -l 2>/dev/null || echo "N/A")
            echo "MPI时间: ${mpi_time} 秒"
            echo "MPI加速比: ${mpi_speedup}x"
        fi
    fi
fi

echo ""
echo "详细结果请查看生成的CSV文件和可视化图表。"
