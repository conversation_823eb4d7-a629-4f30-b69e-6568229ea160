#!/usr/bin/env python3
"""
Enhanced Matrix-Vector Multiplication Performance Visualization
Generate professional performance comparison charts and analysis reports
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
from pathlib import Path
import sys

# Set professional font and style configuration
plt.rcParams['font.family'] = ['Times New Roman', 'serif']
plt.rcParams['font.size'] = 12
plt.rcParams['axes.labelsize'] = 14
plt.rcParams['axes.titlesize'] = 16
plt.rcParams['xtick.labelsize'] = 12
plt.rcParams['ytick.labelsize'] = 12
plt.rcParams['legend.fontsize'] = 12
plt.rcParams['figure.titlesize'] = 18
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['axes.grid'] = True
plt.rcParams['grid.alpha'] = 0.3
plt.rcParams['axes.axisbelow'] = True
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300
plt.rcParams['savefig.bbox'] = 'tight'

# Set seaborn style for better aesthetics
sns.set_style("whitegrid")

class EnhancedPerformanceVisualizer:
    def __init__(self):
        self.results = []
        # Professional color palette
        self.colors = ['#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#6A994E', '#7209B7']
        self.method_colors = {
            'Serial': '#2E86AB',
            'OpenMP': '#F18F01',
            'MPI': '#6A994E'
        }
        
    def load_data(self, filename):
        """Load performance data from CSV file"""
        try:
            df = pd.read_csv(filename)
            self.results.append(df)
            print(f"Successfully loaded data from: {filename}")
            return df
        except FileNotFoundError:
            print(f"Warning: File {filename} not found")
            return None
        except Exception as e:
            print(f"Error loading file {filename}: {e}")
            return None
    
    def combine_data(self):
        """Combine all loaded data"""
        if not self.results:
            print("No data available")
            return None

        combined = pd.concat(self.results, ignore_index=True)
        return self.filter_data(combined)

    def filter_data(self, data):
        """Filter data to show only the best OpenMP result and remove OpenMP_Optimized"""
        filtered_data = []

        for method in data['Method'].unique():
            method_data = data[data['Method'] == method]

            if method == 'OpenMP_Optimized':
                # Rename to OpenMP and use this as the OpenMP result
                method_data = method_data.copy()
                method_data['Method'] = 'OpenMP'
                filtered_data.append(method_data)
            elif method == 'OpenMP':
                # Skip the basic OpenMP version, we'll use the optimized one
                continue
            else:
                # Keep other methods as is
                filtered_data.append(method_data)

        if filtered_data:
            return pd.concat(filtered_data, ignore_index=True)
        else:
            return data
    
    def plot_execution_time_comparison(self, data, save_path="execution_time_comparison.png"):
        """Create execution time comparison chart"""
        fig, ax = plt.subplots(figsize=(12, 8))
        
        # Get best execution time for each method
        methods = data['Method'].unique()
        best_times = []
        thread_counts = []
        colors = []
        
        for method in methods:
            method_data = data[data['Method'] == method]
            best_idx = method_data['ExecutionTime'].idxmin()
            best_times.append(method_data.loc[best_idx, 'ExecutionTime'])
            thread_counts.append(method_data.loc[best_idx, 'NumThreads'])
            colors.append(self.method_colors.get(method, '#666666'))
        
        # Create bar chart
        bars = ax.bar(methods, best_times, color=colors, alpha=0.8, edgecolor='white', linewidth=2)
        
        # Add value labels on bars
        for i, (bar, time, threads) in enumerate(zip(bars, best_times, thread_counts)):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + height*0.02,
                   f'{time:.6f}s\n({threads} threads)', 
                   ha='center', va='bottom', fontweight='bold', fontsize=11)
        
        ax.set_xlabel('Parallelization Method', fontweight='bold')
        ax.set_ylabel('Execution Time (seconds)', fontweight='bold')
        ax.set_title('Performance Comparison: Best Execution Times\nMatrix-Vector Multiplication', 
                    fontweight='bold', pad=20)
        
        # Customize appearance
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.set_ylim(0, max(best_times) * 1.2)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.show()
        print(f"Execution time comparison chart saved: {save_path}")
    
    def plot_speedup_analysis(self, data, save_path="speedup_analysis.png"):
        """Create speedup and efficiency analysis charts"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 7))
        
        # Left plot: Speedup vs Thread Count
        openmp_data = data[data['Method'] == 'OpenMP'].copy()
        if not openmp_data.empty:
            openmp_data = openmp_data.sort_values('NumThreads')
            
            # Plot actual speedup
            ax1.plot(openmp_data['NumThreads'], openmp_data['Speedup'], 
                    'o-', linewidth=3, markersize=10, color=self.method_colors['OpenMP'], 
                    label='Actual Speedup', markeredgecolor='white', markeredgewidth=2)
            
            # Plot ideal speedup line
            max_threads = openmp_data['NumThreads'].max()
            ideal_threads = range(1, max_threads + 1)
            ax1.plot(ideal_threads, ideal_threads, '--', 
                    color='#666666', alpha=0.8, linewidth=2, label='Ideal Speedup')
            
            ax1.set_xlabel('Number of Threads', fontweight='bold')
            ax1.set_ylabel('Speedup Factor', fontweight='bold')
            ax1.set_title('OpenMP Speedup Analysis', fontweight='bold', pad=15)
            ax1.legend(frameon=True, fancybox=True, shadow=True)
            ax1.set_xlim(0.5, max_threads + 0.5)
            ax1.spines['top'].set_visible(False)
            ax1.spines['right'].set_visible(False)
        
        # Right plot: Parallel Efficiency
        if not openmp_data.empty:
            ax2.plot(openmp_data['NumThreads'], openmp_data['Efficiency'] * 100, 
                    's-', linewidth=3, markersize=10, color=self.method_colors['OpenMP_Optimized'], 
                    label='Parallel Efficiency', markeredgecolor='white', markeredgewidth=2)
            
            # Add efficiency threshold lines
            ax2.axhline(y=100, color='#2E86AB', linestyle='-', alpha=0.7, linewidth=2, label='100% Efficiency')
            ax2.axhline(y=80, color='#F18F01', linestyle='--', alpha=0.7, linewidth=2, label='80% Threshold')
            ax2.axhline(y=50, color='#C73E1D', linestyle='--', alpha=0.7, linewidth=2, label='50% Threshold')
            
            ax2.set_xlabel('Number of Threads', fontweight='bold')
            ax2.set_ylabel('Parallel Efficiency (%)', fontweight='bold')
            ax2.set_title('OpenMP Parallel Efficiency', fontweight='bold', pad=15)
            ax2.legend(frameon=True, fancybox=True, shadow=True)
            ax2.set_xlim(0.5, max_threads + 0.5)
            ax2.set_ylim(0, max(openmp_data['Efficiency'] * 100) * 1.1)
            ax2.spines['top'].set_visible(False)
            ax2.spines['right'].set_visible(False)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.show()
        print(f"Speedup analysis chart saved: {save_path}")
    
    def plot_throughput_comparison(self, data, save_path="throughput_comparison.png"):
        """Create computational throughput comparison chart"""
        fig, ax = plt.subplots(figsize=(14, 8))
        
        # Calculate throughput (GFLOPS)
        matrix_size = data['MatrixSize'].iloc[0]
        data_copy = data.copy()
        data_copy['Throughput'] = (2.0 * matrix_size * matrix_size) / data_copy['ExecutionTime'] / 1e9
        
        # Plot different methods
        methods = data_copy['Method'].unique()
        
        for i, method in enumerate(methods):
            method_data = data_copy[data_copy['Method'] == method]
            color = self.method_colors.get(method, self.colors[i])
            
            if method == 'OpenMP':
                # Show OpenMP scaling with thread count
                method_data = method_data.sort_values('NumThreads')
                ax.plot(method_data['NumThreads'], method_data['Throughput'], 
                       'o-', linewidth=3, markersize=10, color=color, label=method,
                       markeredgecolor='white', markeredgewidth=2)
            else:
                # Show single best point for other methods
                best_idx = method_data['Throughput'].idxmax()
                best_throughput = method_data.loc[best_idx, 'Throughput']
                best_threads = method_data.loc[best_idx, 'NumThreads']
                ax.scatter(best_threads, best_throughput, s=200, 
                          color=color, label=f'{method} (Best)', 
                          edgecolors='white', linewidth=2, zorder=5)
        
        ax.set_xlabel('Number of Threads/Processes', fontweight='bold')
        ax.set_ylabel('Computational Throughput (GFLOPS)', fontweight='bold')
        ax.set_title('Computational Throughput Comparison\nMatrix-Vector Multiplication Performance', 
                    fontweight='bold', pad=20)
        ax.legend(frameon=True, fancybox=True, shadow=True)
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.show()
        print(f"Throughput comparison chart saved: {save_path}")
    
    def plot_comprehensive_performance(self, data, save_path="comprehensive_performance.png"):
        """Create comprehensive performance dashboard"""
        fig = plt.figure(figsize=(16, 12))
        gs = fig.add_gridspec(3, 2, height_ratios=[1, 1, 1], hspace=0.3, wspace=0.3)
        
        # 1. Execution Time Comparison
        ax1 = fig.add_subplot(gs[0, :])
        methods = data['Method'].unique()
        best_times = []
        colors = []
        
        for method in methods:
            method_data = data[data['Method'] == method]
            best_idx = method_data['ExecutionTime'].idxmin()
            best_times.append(method_data.loc[best_idx, 'ExecutionTime'])
            colors.append(self.method_colors.get(method, '#666666'))
        
        bars = ax1.bar(methods, best_times, color=colors, alpha=0.8, edgecolor='white', linewidth=2)
        for bar, time in zip(bars, best_times):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.02,
                    f'{time:.6f}s', ha='center', va='bottom', fontweight='bold')
        
        ax1.set_ylabel('Execution Time (s)', fontweight='bold')
        ax1.set_title('A. Best Execution Times by Method', fontweight='bold', loc='left')
        ax1.spines['top'].set_visible(False)
        ax1.spines['right'].set_visible(False)
        
        # 2. Speedup Analysis
        ax2 = fig.add_subplot(gs[1, 0])
        openmp_data = data[data['Method'] == 'OpenMP'].copy()
        if not openmp_data.empty:
            openmp_data = openmp_data.sort_values('NumThreads')
            ax2.plot(openmp_data['NumThreads'], openmp_data['Speedup'], 
                    'o-', linewidth=3, markersize=8, color=self.method_colors['OpenMP'])
            max_threads = openmp_data['NumThreads'].max()
            ideal_threads = range(1, max_threads + 1)
            ax2.plot(ideal_threads, ideal_threads, '--', color='#666666', alpha=0.7)
        
        ax2.set_xlabel('Threads', fontweight='bold')
        ax2.set_ylabel('Speedup', fontweight='bold')
        ax2.set_title('B. OpenMP Speedup', fontweight='bold', loc='left')
        ax2.spines['top'].set_visible(False)
        ax2.spines['right'].set_visible(False)
        
        # 3. Efficiency Analysis
        ax3 = fig.add_subplot(gs[1, 1])
        if not openmp_data.empty:
            ax3.plot(openmp_data['NumThreads'], openmp_data['Efficiency'] * 100, 
                    's-', linewidth=3, markersize=8, color=self.method_colors['OpenMP_Optimized'])
            ax3.axhline(y=100, color='#2E86AB', linestyle='-', alpha=0.7)
            ax3.axhline(y=80, color='#F18F01', linestyle='--', alpha=0.7)
        
        ax3.set_xlabel('Threads', fontweight='bold')
        ax3.set_ylabel('Efficiency (%)', fontweight='bold')
        ax3.set_title('C. Parallel Efficiency', fontweight='bold', loc='left')
        ax3.spines['top'].set_visible(False)
        ax3.spines['right'].set_visible(False)
        
        # 4. Throughput Comparison
        ax4 = fig.add_subplot(gs[2, :])
        matrix_size = data['MatrixSize'].iloc[0]
        data_copy = data.copy()
        data_copy['Throughput'] = (2.0 * matrix_size * matrix_size) / data_copy['ExecutionTime'] / 1e9
        
        for method in methods:
            method_data = data_copy[data_copy['Method'] == method]
            color = self.method_colors.get(method, '#666666')
            
            if method == 'OpenMP':
                method_data = method_data.sort_values('NumThreads')
                ax4.plot(method_data['NumThreads'], method_data['Throughput'], 
                        'o-', linewidth=3, markersize=8, color=color, label=method)
            else:
                best_idx = method_data['Throughput'].idxmax()
                best_throughput = method_data.loc[best_idx, 'Throughput']
                best_threads = method_data.loc[best_idx, 'NumThreads']
                ax4.scatter(best_threads, best_throughput, s=150, 
                           color=color, label=method, zorder=5)
        
        ax4.set_xlabel('Threads/Processes', fontweight='bold')
        ax4.set_ylabel('Throughput (GFLOPS)', fontweight='bold')
        ax4.set_title('D. Computational Throughput', fontweight='bold', loc='left')
        ax4.legend(frameon=True, fancybox=True, shadow=True)
        ax4.spines['top'].set_visible(False)
        ax4.spines['right'].set_visible(False)
        
        plt.suptitle('Matrix-Vector Multiplication: Comprehensive Performance Analysis', 
                    fontsize=20, fontweight='bold', y=0.98)
        
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.show()
        print(f"Comprehensive performance dashboard saved: {save_path}")

def main():
    print("Enhanced Matrix-Vector Multiplication Performance Visualization")
    print("=" * 65)
    
    visualizer = EnhancedPerformanceVisualizer()
    
    # Load data files
    data_files = ['serial_performance.csv', 'openmp_performance.csv', 'mpi_performance.csv']
    
    for filename in data_files:
        visualizer.load_data(filename)
    
    # Combine data
    combined_data = visualizer.combine_data()
    
    if combined_data is None or combined_data.empty:
        print("No performance data available. Please run performance tests first.")
        return
    
    print(f"\nSuccessfully loaded {len(combined_data)} performance records")
    print("Generating professional visualization charts...")
    
    # Generate all charts
    visualizer.plot_execution_time_comparison(combined_data)
    visualizer.plot_speedup_analysis(combined_data)
    visualizer.plot_throughput_comparison(combined_data)
    visualizer.plot_comprehensive_performance(combined_data)
    
    print("\nAll visualization charts have been generated successfully!")
    print("Charts saved:")
    print("- execution_time_comparison.png")
    print("- speedup_analysis.png") 
    print("- throughput_comparison.png")
    print("- comprehensive_performance.png")

if __name__ == "__main__":
    main()
