矩阵向量乘法并行性能分析报告
==================================================

测试矩阵大小: 1000 x 1000
测试方法数量: 4
总测试配置: 7

各方法最佳性能:
------------------------------
Serial:
  最佳执行时间: 0.001690 秒
  使用线程数: 1
  加速比: 1.00
  并行效率: 100.0%
  计算吞吐量: 1.18 GFLOPS

OpenMP:
  最佳执行时间: 0.000151 秒
  使用线程数: 8
  加速比: 11.21
  并行效率: 140.2%
  计算吞吐量: 13.27 GFLOPS

OpenMP_Optimized:
  最佳执行时间: 0.000234 秒
  使用线程数: 8
  加速比: 7.22
  并行效率: 90.3%
  计算吞吐量: 8.55 GFLOPS

MPI:
  最佳执行时间: 0.000275 秒
  使用线程数: 4
  加速比: 6.15
  并行效率: 153.8%
  计算吞吐量: 7.28 GFLOPS

性能排名 (按执行时间):
------------------------------
1. OpenMP (8 线程): 0.000151 秒
2. OpenMP_Optimized (8 线程): 0.000234 秒
3. MPI (4 线程): 0.000275 秒
4. Serial (1 线程): 0.001690 秒

报告生成时间: 2025-06-09 13:50:21.127600
