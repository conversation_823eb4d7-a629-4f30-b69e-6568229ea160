矩阵向量乘法并行性能分析报告
==================================================

测试矩阵大小: 1000 x 1000
测试方法数量: 4
总测试配置: 7

各方法最佳性能:
------------------------------
Serial:
  最佳执行时间: 0.001190 秒
  使用线程数: 1
  加速比: 1.00
  并行效率: 100.0%
  计算吞吐量: 1.68 GFLOPS

OpenMP:
  最佳执行时间: 0.000165 秒
  使用线程数: 8
  加速比: 7.21
  并行效率: 90.2%
  计算吞吐量: 12.12 GFLOPS

OpenMP_Optimized:
  最佳执行时间: 0.000110 秒
  使用线程数: 8
  加速比: 10.82
  并行效率: 135.3%
  计算吞吐量: 18.18 GFLOPS

MPI:
  最佳执行时间: 0.000310 秒
  使用线程数: 4
  加速比: 3.84
  并行效率: 96.1%
  计算吞吐量: 6.46 GFLOPS

性能排名 (按执行时间):
------------------------------
1. OpenMP_Optimized (8 线程): 0.000110 秒
2. OpenMP (8 线程): 0.000165 秒
3. MPI (4 线程): 0.000310 秒
4. Serial (1 线程): 0.001190 秒

报告生成时间: 2025-06-08 21:36:53.918219
