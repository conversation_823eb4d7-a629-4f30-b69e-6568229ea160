矩阵向量乘法并行性能分析报告
==================================================

测试矩阵大小: 1000 x 1000
测试方法数量: 4
总测试配置: 7

各方法最佳性能:
------------------------------
Serial:
  最佳执行时间: 0.000829 秒
  使用线程数: 1
  加速比: 1.00
  并行效率: 100.0%
  计算吞吐量: 2.41 GFLOPS

OpenMP:
  最佳执行时间: 0.000221 秒
  使用线程数: 8
  加速比: 3.75
  并行效率: 46.9%
  计算吞吐量: 9.04 GFLOPS

OpenMP_Optimized:
  最佳执行时间: 0.000107 秒
  使用线程数: 8
  加速比: 7.74
  并行效率: 96.7%
  计算吞吐量: 18.66 GFLOPS

MPI:
  最佳执行时间: 0.000281 秒
  使用线程数: 4
  加速比: 2.95
  并行效率: 73.7%
  计算吞吐量: 7.11 GFLOPS

性能排名 (按执行时间):
------------------------------
1. OpenMP_Optimized (8 线程): 0.000107 秒
2. OpenMP (8 线程): 0.000221 秒
3. MPI (4 线程): 0.000281 秒
4. Serial (1 线程): 0.000829 秒

报告生成时间: 2025-06-09 13:48:20.151167
