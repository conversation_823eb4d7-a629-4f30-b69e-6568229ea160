#ifndef UTILS_H
#define UTILS_H

#include <vector>
#include <string>
#include <chrono>

// 矩阵和向量的数据类型定义
using Matrix = std::vector<std::vector<double>>;
using Vector = std::vector<double>;

// 性能测试结果结构体
struct PerformanceResult {
    std::string method;
    int matrix_size;
    int num_threads;
    double execution_time;
    double speedup;
    double efficiency;
};

// 工具函数声明
class MatrixUtils {
public:
    // 矩阵和向量操作
    static Matrix generateRandomMatrix(int rows, int cols, double min_val = 0.0, double max_val = 1.0);
    static Vector generateRandomVector(int size, double min_val = 0.0, double max_val = 1.0);
    static Vector matrixVectorMultiply(const Matrix& matrix, const Vector& vector);
    
    // 输入输出函数
    static void printMatrix(const Matrix& matrix, const std::string& title = "Matrix");
    static void printVector(const Vector& vector, const std::string& title = "Vector");
    static void saveMatrixToFile(const Matrix& matrix, const std::string& filename);
    static void saveVectorToFile(const Vector& vector, const std::string& filename);
    static Matrix loadMatrixFromFile(const std::string& filename);
    static Vector loadVectorFromFile(const std::string& filename);
    
    // 验证函数
    static bool compareVectors(const Vector& v1, const Vector& v2, double tolerance = 1e-9);
    static double calculateError(const Vector& v1, const Vector& v2);
    
    // 性能测试工具
    static void savePerformanceResults(const std::vector<PerformanceResult>& results, 
                                     const std::string& filename);
};

// 计时器类
class Timer {
private:
    std::chrono::high_resolution_clock::time_point start_time;
    
public:
    void start();
    double stop(); // 返回经过的时间（秒）
};

// 常量定义
const int DEFAULT_MATRIX_SIZE = 1000;
const int MAX_MATRIX_SIZE = 5000;
const double TOLERANCE = 1e-9;

#endif // UTILS_H
