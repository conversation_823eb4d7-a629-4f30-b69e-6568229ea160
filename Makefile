# Makefile for Matrix-Vector Multiplication Project

# 编译器设置
CXX = g++
MPICXX = mpic++
CXXFLAGS = -std=c++11 -O3 -Wall -Wextra
OPENMP_FLAGS = -fopenmp
MPI_FLAGS = 

# 目标文件
TARGETS = serial_matrix_vector mpi_matrix_vector openmp_matrix_vector performance_test

# 源文件
UTILS_SRC = utils.cpp
SERIAL_SRC = serial_matrix_vector.cpp
MPI_SRC = mpi_matrix_vector.cpp
OPENMP_SRC = openmp_matrix_vector.cpp
PERF_SRC = performance_test.cpp

# 默认目标
all: $(TARGETS)

# 串行版本
serial_matrix_vector: $(SERIAL_SRC) $(UTILS_SRC)
	$(CXX) $(CXXFLAGS) -o $@ $^

# MPI版本
mpi_matrix_vector: $(MPI_SRC) $(UTILS_SRC)
	$(MPICXX) $(CXXFLAGS) $(MPI_FLAGS) -o $@ $^

# OpenMP版本
openmp_matrix_vector: $(OPENMP_SRC) $(UTILS_SRC)
	$(CXX) $(CXXFLAGS) $(OPENMP_FLAGS) -o $@ $^

# 性能测试程序
performance_test: $(PERF_SRC) $(UTILS_SRC)
	$(CXX) $(CXXFLAGS) $(OPENMP_FLAGS) -o $@ $^

# 清理
clean:
	rm -f $(TARGETS)
	rm -f *.dat *.csv
	rm -f *.o

# 运行测试
test: all
	@echo "=== 运行完整测试套件 ==="
	@echo "1. 串行测试..."
	./serial_matrix_vector 1000 3
	@echo "2. OpenMP测试..."
	./openmp_matrix_vector 8 3
	@echo "3. MPI测试 (需要mpirun)..."
	@echo "   使用命令: mpirun -np 4 ./mpi_matrix_vector"
	@echo "4. 性能对比测试..."
	./performance_test

# 快速测试（小矩阵）
quick_test: all
	@echo "=== 快速测试 (小矩阵) ==="
	./serial_matrix_vector 100 1
	./openmp_matrix_vector 4 1
	@echo "MPI测试: mpirun -np 2 ./mpi_matrix_vector"

# 性能基准测试
benchmark: all
	@echo "=== 性能基准测试 ==="
	./serial_matrix_vector 2000 5
	./openmp_matrix_vector 8 5
	@echo "MPI基准测试: mpirun -np 4 ./mpi_matrix_vector"
	./performance_test 2000

# 安装依赖检查
check_deps:
	@echo "检查编译依赖..."
	@which $(CXX) > /dev/null || (echo "错误: 未找到 g++ 编译器" && exit 1)
	@which $(MPICXX) > /dev/null || (echo "警告: 未找到 MPI 编译器 (mpic++)")
	@echo "依赖检查完成"

# 帮助信息
help:
	@echo "可用的make目标:"
	@echo "  all          - 编译所有程序"
	@echo "  serial_matrix_vector - 编译串行版本"
	@echo "  mpi_matrix_vector    - 编译MPI版本"
	@echo "  openmp_matrix_vector - 编译OpenMP版本"
	@echo "  performance_test     - 编译性能测试程序"
	@echo "  test         - 运行完整测试"
	@echo "  quick_test   - 运行快速测试"
	@echo "  benchmark    - 运行性能基准测试"
	@echo "  check_deps   - 检查编译依赖"
	@echo "  clean        - 清理编译文件和结果文件"
	@echo "  help         - 显示此帮助信息"
	@echo ""
	@echo "使用示例:"
	@echo "  make all                    # 编译所有程序"
	@echo "  make test                   # 运行测试"
	@echo "  make serial_matrix_vector   # 只编译串行版本"
	@echo "  ./serial_matrix_vector 1000 5  # 运行串行版本(1000x1000矩阵,5次迭代)"
	@echo "  ./openmp_matrix_vector 8 5     # 运行OpenMP版本(最大8线程,5次迭代)"
	@echo "  mpirun -np 4 ./mpi_matrix_vector  # 运行MPI版本(4进程)"

# 伪目标
.PHONY: all clean test quick_test benchmark check_deps help
