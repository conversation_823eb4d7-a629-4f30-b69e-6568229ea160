#!/usr/bin/env python3
"""
系统环境和Python包版本检查工具
"""

import sys
import platform
import subprocess
import importlib.util

def check_python_info():
    """检查Python基本信息"""
    print("=" * 60)
    print("🐍 PYTHON 环境信息")
    print("=" * 60)
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"平台信息: {platform.platform()}")
    print(f"架构信息: {platform.machine()}")
    print(f"处理器: {platform.processor()}")
    print()

def check_package_version(package_name, import_name=None):
    """检查Python包版本"""
    if import_name is None:
        import_name = package_name
    
    try:
        module = __import__(import_name)
        version = getattr(module, '__version__', 'Unknown')
        print(f"✅ {package_name:<15} {version}")
        return True
    except ImportError:
        print(f"❌ {package_name:<15} 未安装")
        return False

def check_python_packages():
    """检查关键Python包"""
    print("📦 PYTHON 包版本检查")
    print("=" * 60)
    
    packages = [
        ('numpy', 'numpy'),
        ('pandas', 'pandas'),
        ('matplotlib', 'matplotlib'),
        ('seaborn', 'seaborn'),
        ('scipy', 'scipy'),
        ('sklearn', 'sklearn'),
        ('jupyter', 'jupyter'),
        ('ipython', 'IPython'),
        ('pip', 'pip'),
    ]
    
    installed_count = 0
    for package_name, import_name in packages:
        if check_package_version(package_name, import_name):
            installed_count += 1
    
    print(f"\n📊 已安装包数量: {installed_count}/{len(packages)}")
    print()

def check_system_tools():
    """检查系统工具"""
    print("🔧 系统工具版本检查")
    print("=" * 60)
    
    tools = [
        ('gcc', 'gcc --version'),
        ('g++', 'g++ --version'),
        ('make', 'make --version'),
        ('mpirun', 'mpirun --version'),
        ('mpic++', 'mpic++ --version'),
        ('git', 'git --version'),
    ]
    
    for tool_name, command in tools:
        try:
            result = subprocess.run(command.split(), 
                                  capture_output=True, 
                                  text=True, 
                                  timeout=5)
            if result.returncode == 0:
                version_line = result.stdout.split('\n')[0]
                print(f"✅ {tool_name:<10} {version_line}")
            else:
                print(f"❌ {tool_name:<10} 命令执行失败")
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print(f"❌ {tool_name:<10} 未找到")
    print()

def check_conda_info():
    """检查Conda环境信息"""
    print("🐍 CONDA 环境信息")
    print("=" * 60)
    
    try:
        # 检查是否在conda环境中
        conda_env = subprocess.run(['conda', 'info', '--envs'], 
                                 capture_output=True, text=True, timeout=10)
        if conda_env.returncode == 0:
            print("Conda环境列表:")
            for line in conda_env.stdout.split('\n'):
                if line.strip() and not line.startswith('#'):
                    if '*' in line:
                        print(f"✅ {line} (当前环境)")
                    else:
                        print(f"   {line}")
        
        # 检查当前环境的包
        current_env = subprocess.run(['conda', 'list'], 
                                   capture_output=True, text=True, timeout=10)
        if current_env.returncode == 0:
            lines = current_env.stdout.split('\n')
            package_count = len([l for l in lines if l.strip() and not l.startswith('#')])
            print(f"\n📦 当前环境包数量: {package_count}")
            
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ Conda未安装或不可用")
    print()

def check_hardware_info():
    """检查硬件信息"""
    print("💻 硬件信息")
    print("=" * 60)
    
    try:
        # CPU信息
        cpu_info = subprocess.run(['lscpu'], capture_output=True, text=True, timeout=5)
        if cpu_info.returncode == 0:
            for line in cpu_info.stdout.split('\n'):
                if 'Model name:' in line:
                    print(f"🔥 CPU: {line.split(':', 1)[1].strip()}")
                elif 'CPU(s):' in line and 'NUMA' not in line:
                    print(f"🔢 CPU核心数: {line.split(':', 1)[1].strip()}")
                elif 'Thread(s) per core:' in line:
                    print(f"🧵 每核心线程数: {line.split(':', 1)[1].strip()}")
        
        # 内存信息
        mem_info = subprocess.run(['free', '-h'], capture_output=True, text=True, timeout=5)
        if mem_info.returncode == 0:
            lines = mem_info.stdout.split('\n')
            if len(lines) > 1:
                mem_line = lines[1].split()
                if len(mem_line) > 1:
                    print(f"💾 总内存: {mem_line[1]}")
        
        # 磁盘信息
        disk_info = subprocess.run(['df', '-h', '/'], capture_output=True, text=True, timeout=5)
        if disk_info.returncode == 0:
            lines = disk_info.stdout.split('\n')
            if len(lines) > 1:
                disk_line = lines[1].split()
                if len(disk_line) > 3:
                    print(f"💽 根分区: {disk_line[1]} 总计, {disk_line[2]} 已用, {disk_line[3]} 可用")
                    
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ 无法获取硬件信息")
    print()

def check_project_files():
    """检查项目文件状态"""
    print("📁 项目文件状态")
    print("=" * 60)
    
    import os
    import glob
    
    # 检查源代码文件
    source_files = [
        'utils.h', 'utils.cpp',
        'serial_matrix_vector.cpp',
        'openmp_matrix_vector.cpp', 
        'mpi_matrix_vector.cpp',
        'performance_test.cpp',
        'Makefile'
    ]
    
    print("源代码文件:")
    for file in source_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"✅ {file:<25} ({size:,} bytes)")
        else:
            print(f"❌ {file:<25} 缺失")
    
    # 检查可执行文件
    print("\n可执行文件:")
    executables = ['serial_matrix_vector', 'openmp_matrix_vector', 'mpi_matrix_vector', 'performance_test']
    for exe in executables:
        if os.path.exists(exe):
            size = os.path.getsize(exe)
            print(f"✅ {exe:<25} ({size:,} bytes)")
        else:
            print(f"❌ {exe:<25} 未编译")
    
    # 检查数据文件
    print("\n数据文件:")
    data_files = glob.glob('*.csv') + glob.glob('*.dat')
    if data_files:
        for file in sorted(data_files):
            size = os.path.getsize(file)
            print(f"📊 {file:<25} ({size:,} bytes)")
    else:
        print("❌ 无数据文件")
    
    # 检查图像文件
    print("\n图像文件:")
    image_files = glob.glob('*.png') + glob.glob('*.jpg')
    if image_files:
        for file in sorted(image_files):
            size = os.path.getsize(file)
            print(f"🖼️  {file:<25} ({size:,} bytes)")
    else:
        print("❌ 无图像文件")
    print()

def main():
    """主函数"""
    print("🔍 系统环境全面检查工具")
    print("=" * 60)
    print()
    
    check_python_info()
    check_python_packages()
    check_system_tools()
    check_conda_info()
    check_hardware_info()
    check_project_files()
    
    print("🎉 环境检查完成!")
    print("=" * 60)

if __name__ == "__main__":
    main()
