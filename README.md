# 矩阵向量乘法并行计算实现与性能分析

本项目实现了矩阵向量乘法的多种并行计算方案，包括串行、OpenMP和MPI实现，并提供了完整的性能分析和可视化工具。

## 项目概述

矩阵向量乘法是线性代数中的基础运算，其数学表达式为：

```
C_i = Σ(j=0 to n-1) a_ij * b_j, i = 0,1,...,m-1
```

本项目通过不同的并行化策略来加速这一计算过程，并对各种方法的性能进行深入分析。

## 文件结构

```
矩阵向量相乘/
├── utils.h                    # 通用工具函数头文件
├── utils.cpp                  # 通用工具函数实现
├── serial_matrix_vector.cpp   # 串行实现
├── mpi_matrix_vector.cpp      # MPI并行实现
├── openmp_matrix_vector.cpp   # OpenMP并行实现
├── performance_test.cpp       # 性能测试程序
├── visualize_results.py       # 结果可视化脚本
├── Makefile                   # 编译脚本
├── run_tests.sh              # 自动化测试脚本
└── README.md                 # 项目说明文档
```

## 功能特性

### 1. 多种并行化实现
- **串行版本**: 作为性能基准的传统实现
- **OpenMP版本**: 基于共享内存的多线程并行
- **MPI版本**: 基于消息传递的分布式并行

### 2. 性能分析工具
- 执行时间测量
- 加速比计算
- 并行效率分析
- 吞吐量评估

### 3. 可视化报告
- 性能对比图表
- 加速比分析图
- 并行效率热力图
- 详细的文本报告

## 编译要求

### 必需依赖
- GCC 编译器 (支持 C++11)
- Make 工具

### 可选依赖
- OpenMPI 或其他MPI实现 (用于MPI版本)
- Python3 + matplotlib, pandas, numpy, seaborn (用于可视化)

## 快速开始

### 1. 编译所有程序
```bash
make all
```

### 2. 运行完整测试套件
```bash
chmod +x run_tests.sh
./run_tests.sh
```

### 3. 自定义参数测试
```bash
# 指定矩阵大小和迭代次数
./run_tests.sh --size 2000 --iterations 10 --threads 16 --processes 8
```

## 详细使用说明

### 单独运行各个程序

#### 串行版本
```bash
./serial_matrix_vector [矩阵大小] [迭代次数]
# 示例: ./serial_matrix_vector 1000 5
```

#### OpenMP版本
```bash
./openmp_matrix_vector [最大线程数] [迭代次数]
# 示例: ./openmp_matrix_vector 8 5
```

#### MPI版本
```bash
mpirun -np [进程数] ./mpi_matrix_vector
# 示例: mpirun -np 4 ./mpi_matrix_vector
```

#### 性能分析
```bash
./performance_test [矩阵大小]
# 示例: ./performance_test 1000
```

### 生成可视化报告
```bash
python3 visualize_results.py
```

## 输出文件说明

### 数据文件 (*.dat)
- `test_matrix.dat`: 测试矩阵数据
- `test_vector.dat`: 测试向量数据
- `serial_result.dat`: 串行计算结果
- `openmp_result.dat`: OpenMP计算结果
- `mpi_result.dat`: MPI计算结果

### 性能结果 (*.csv)
- `serial_performance.csv`: 串行性能数据
- `openmp_performance.csv`: OpenMP性能数据
- `mpi_performance.csv`: MPI性能数据

### 可视化图表 (*.png)
- `execution_time_comparison.png`: 执行时间对比
- `speedup_analysis.png`: 加速比分析
- `throughput_comparison.png`: 吞吐量对比
- `efficiency_heatmap.png`: 效率热力图

### 报告文件 (*.txt)
- `performance_summary.txt`: 性能总结报告

## 性能优化建议

1. **线程数设置**: 建议设置为CPU核心数，避免过度订阅
2. **内存访问**: 注意缓存友好的访问模式
3. **负载均衡**: 确保各线程/进程的工作负载均匀
4. **NUMA优化**: 在多NUMA节点系统上考虑内存亲和性

## 实验结果分析

项目会自动生成详细的性能分析报告，包括：

- 各方法的最佳执行时间
- 加速比和并行效率
- 可扩展性分析
- 优化建议

## 故障排除

### 编译问题
- 确保安装了GCC和Make
- 对于MPI版本，确保安装了MPI开发包

### 运行时问题
- 检查系统内存是否足够
- 确保线程数不超过系统核心数
- 验证MPI环境配置

### 可视化问题
- 安装Python依赖: `pip3 install matplotlib pandas numpy seaborn`
- 确保有图形界面支持

## 扩展功能

项目设计具有良好的可扩展性，可以轻松添加：

- 新的并行化策略 (如CUDA、OpenCL)
- 不同的矩阵存储格式
- 更多的性能指标
- 其他线性代数运算

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献指南

欢迎提交Issue和Pull Request来改进项目。请确保：

1. 代码符合项目的编码规范
2. 添加适当的测试用例
3. 更新相关文档

## 联系信息

如有问题或建议，请通过GitHub Issues联系。
