#include "utils.h"
#include <mpi.h>
#include <iostream>
#include <iomanip>
#include <fstream>
#include <sstream>

/**
 * MPI并行矩阵向量乘法实现
 * 使用行分块的方式进行并行计算
 */
class MPIMatrixVector {
private:
    int rank;
    int size;
    
public:
    MPIMatrixVector() {
        MPI_Comm_rank(MPI_COMM_WORLD, &rank);
        MPI_Comm_size(MPI_COMM_WORLD, &size);
    }
    
    Vector multiply(const Matrix& matrix, const Vector& vector) {
        int rows = matrix.size();
        int cols = matrix[0].size();
        
        if (cols != static_cast<int>(vector.size())) {
            throw std::invalid_argument("Matrix columns must equal vector size");
        }
        
        // 计算每个进程处理的行数
        int local_rows = rows / size;
        int remainder = rows % size;
        
        // 调整本地行数（处理不能整除的情况）
        if (rank < remainder) {
            local_rows++;
        }
        
        // 计算本地矩阵的起始行
        int start_row = rank * (rows / size) + std::min(rank, remainder);
        
        // 创建本地结果向量
        Vector local_result(local_rows, 0.0);
        
        // 广播向量到所有进程
        Vector global_vector = vector;
        MPI_Bcast(global_vector.data(), cols, MPI_DOUBLE, 0, MPI_COMM_WORLD);
        
        // 本地矩阵向量乘法
        for (int i = 0; i < local_rows; ++i) {
            int global_row = start_row + i;
            for (int j = 0; j < cols; ++j) {
                local_result[i] += matrix[global_row][j] * global_vector[j];
            }
        }
        
        // 收集所有进程的结果
        Vector global_result(rows);
        
        // 准备接收缓冲区的大小和偏移量
        std::vector<int> recvcounts(size);
        std::vector<int> displs(size);
        
        for (int i = 0; i < size; ++i) {
            int proc_rows = rows / size;
            if (i < remainder) proc_rows++;
            recvcounts[i] = proc_rows;
            displs[i] = (i == 0) ? 0 : displs[i-1] + recvcounts[i-1];
        }
        
        MPI_Gatherv(local_result.data(), local_rows, MPI_DOUBLE,
                   global_result.data(), recvcounts.data(), displs.data(),
                   MPI_DOUBLE, 0, MPI_COMM_WORLD);
        
        return global_result;
    }
    
    double benchmark(const Matrix& matrix, const Vector& vector, int iterations = 1) {
        double start_time, end_time;
        Vector result;
        
        MPI_Barrier(MPI_COMM_WORLD);
        start_time = MPI_Wtime();
        
        for (int iter = 0; iter < iterations; ++iter) {
            result = multiply(matrix, vector);
        }
        
        MPI_Barrier(MPI_COMM_WORLD);
        end_time = MPI_Wtime();
        
        return (end_time - start_time) / iterations;
    }
    
    void readMatrix(const std::string& filename, Matrix& matrix, int& rows, int& cols) {
        if (rank == 0) {
            matrix = MatrixUtils::loadMatrixFromFile(filename);
            rows = matrix.size();
            cols = matrix[0].size();
        }
        
        // 广播矩阵维度
        MPI_Bcast(&rows, 1, MPI_INT, 0, MPI_COMM_WORLD);
        MPI_Bcast(&cols, 1, MPI_INT, 0, MPI_COMM_WORLD);
        
        // 非根进程分配矩阵空间
        if (rank != 0) {
            matrix.resize(rows, Vector(cols));
        }
        
        // 广播矩阵数据
        for (int i = 0; i < rows; ++i) {
            MPI_Bcast(matrix[i].data(), cols, MPI_DOUBLE, 0, MPI_COMM_WORLD);
        }
    }
    
    void readVector(const std::string& filename, Vector& vector) {
        int size_vec;
        
        if (rank == 0) {
            vector = MatrixUtils::loadVectorFromFile(filename);
            size_vec = vector.size();
        }
        
        // 广播向量大小
        MPI_Bcast(&size_vec, 1, MPI_INT, 0, MPI_COMM_WORLD);
        
        // 非根进程分配向量空间
        if (rank != 0) {
            vector.resize(size_vec);
        }
        
        // 广播向量数据
        MPI_Bcast(vector.data(), size_vec, MPI_DOUBLE, 0, MPI_COMM_WORLD);
    }
};

int main(int argc, char* argv[]) {
    MPI_Init(&argc, &argv);
    
    MPIMatrixVector mpi_solver;
    int rank, size;
    MPI_Comm_rank(MPI_COMM_WORLD, &rank);
    MPI_Comm_size(MPI_COMM_WORLD, &size);
    
    if (rank == 0) {
        std::cout << "=== MPI并行矩阵向量乘法测试 ===" << std::endl;
        std::cout << "进程数: " << size << std::endl;
    }
    
    try {
        Matrix matrix;
        Vector vector;
        int rows, cols;
        int iterations = 5;
        
        // 读取测试数据
        mpi_solver.readMatrix("test_matrix.dat", matrix, rows, cols);
        mpi_solver.readVector("test_vector.dat", vector);
        
        if (rank == 0) {
            std::cout << "矩阵大小: " << rows << "x" << cols << std::endl;
            std::cout << "迭代次数: " << iterations << std::endl;
        }
        
        // 执行MPI并行计算
        if (rank == 0) {
            std::cout << "\n正在执行MPI并行计算..." << std::endl;
        }
        
        double avg_time = mpi_solver.benchmark(matrix, vector, iterations);
        Vector result = mpi_solver.multiply(matrix, vector);
        
        if (rank == 0) {
            // 保存结果
            MatrixUtils::saveVectorToFile(result, "mpi_result.dat");
            
            // 验证结果正确性
            Vector serial_result = MatrixUtils::loadVectorFromFile("serial_result.dat");
            bool is_correct = MatrixUtils::compareVectors(result, serial_result, TOLERANCE);
            double error = MatrixUtils::calculateError(result, serial_result);
            
            // 显示结果
            if (rows <= 10) {
                MatrixUtils::printVector(result, "MPI计算结果");
            }
            
            // 性能统计
            std::cout << "\n=== 性能统计 ===" << std::endl;
            std::cout << std::fixed << std::setprecision(6);
            std::cout << "平均执行时间: " << avg_time << " 秒" << std::endl;
            std::cout << "计算吞吐量: " << (2.0 * rows * cols) / avg_time / 1e9 
                      << " GFLOPS" << std::endl;
            std::cout << "结果正确性: " << (is_correct ? "正确" : "错误") << std::endl;
            std::cout << "最大误差: " << error << std::endl;
            
            // 计算加速比（需要串行时间）
            std::ifstream serial_file("serial_performance.csv");
            double serial_time = 0.0;
            if (serial_file.is_open()) {
                std::string line;
                std::getline(serial_file, line); // 跳过标题行
                if (std::getline(serial_file, line)) {
                    size_t pos = line.find_last_of(',');
                    if (pos != std::string::npos) {
                        pos = line.find_last_of(',', pos - 1);
                        if (pos != std::string::npos) {
                            pos = line.find_last_of(',', pos - 1);
                            if (pos != std::string::npos) {
                                serial_time = std::stod(line.substr(pos + 1, 
                                    line.find(',', pos + 1) - pos - 1));
                            }
                        }
                    }
                }
                serial_file.close();
            }
            
            double speedup = (serial_time > 0) ? serial_time / avg_time : 0.0;
            double efficiency = speedup / size;
            
            if (speedup > 0) {
                std::cout << "加速比: " << speedup << std::endl;
                std::cout << "并行效率: " << efficiency * 100 << "%" << std::endl;
            }
            
            // 保存性能结果
            std::vector<PerformanceResult> results;
            PerformanceResult mpi_result;
            mpi_result.method = "MPI";
            mpi_result.matrix_size = rows;
            mpi_result.num_threads = size;
            mpi_result.execution_time = avg_time;
            mpi_result.speedup = speedup;
            mpi_result.efficiency = efficiency;
            results.push_back(mpi_result);
            
            MatrixUtils::savePerformanceResults(results, "mpi_performance.csv");
            
            std::cout << "\nMPI并行计算完成！结果已保存到文件。" << std::endl;
        }
        
    } catch (const std::exception& e) {
        if (rank == 0) {
            std::cerr << "错误: " << e.what() << std::endl;
        }
        MPI_Finalize();
        return 1;
    }
    
    MPI_Finalize();
    return 0;
}
