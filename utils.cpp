#include "utils.h"
#include <iostream>
#include <fstream>
#include <random>
#include <iomanip>
#include <cmath>
#include <algorithm>

// 生成随机矩阵
Matrix MatrixUtils::generateRandomMatrix(int rows, int cols, double min_val, double max_val) {
    Matrix matrix(rows, Vector(cols));
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_real_distribution<double> dis(min_val, max_val);
    
    for (int i = 0; i < rows; ++i) {
        for (int j = 0; j < cols; ++j) {
            matrix[i][j] = dis(gen);
        }
    }
    return matrix;
}

// 生成随机向量
Vector MatrixUtils::generateRandomVector(int size, double min_val, double max_val) {
    Vector vector(size);
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_real_distribution<double> dis(min_val, max_val);
    
    for (int i = 0; i < size; ++i) {
        vector[i] = dis(gen);
    }
    return vector;
}

// 串行矩阵向量乘法
Vector MatrixUtils::matrixVectorMultiply(const Matrix& matrix, const Vector& vector) {
    int rows = matrix.size();
    int cols = matrix[0].size();
    Vector result(rows, 0.0);
    
    for (int i = 0; i < rows; ++i) {
        for (int j = 0; j < cols; ++j) {
            result[i] += matrix[i][j] * vector[j];
        }
    }
    return result;
}

// 打印矩阵
void MatrixUtils::printMatrix(const Matrix& matrix, const std::string& title) {
    std::cout << "\n" << title << ":\n";
    int rows = std::min(5, static_cast<int>(matrix.size()));
    int cols = std::min(5, static_cast<int>(matrix[0].size()));
    
    for (int i = 0; i < rows; ++i) {
        for (int j = 0; j < cols; ++j) {
            std::cout << std::setw(8) << std::fixed << std::setprecision(3) 
                      << matrix[i][j] << " ";
        }
        if (matrix[0].size() > 5) std::cout << "...";
        std::cout << "\n";
    }
    if (matrix.size() > 5) std::cout << "...\n";
}

// 打印向量
void MatrixUtils::printVector(const Vector& vector, const std::string& title) {
    std::cout << "\n" << title << ":\n";
    int size = std::min(10, static_cast<int>(vector.size()));
    
    for (int i = 0; i < size; ++i) {
        std::cout << std::setw(8) << std::fixed << std::setprecision(3) 
                  << vector[i] << " ";
    }
    if (vector.size() > 10) std::cout << "...";
    std::cout << "\n";
}

// 保存矩阵到文件
void MatrixUtils::saveMatrixToFile(const Matrix& matrix, const std::string& filename) {
    std::ofstream file(filename);
    if (!file.is_open()) {
        std::cerr << "Error: Cannot open file " << filename << std::endl;
        return;
    }
    
    file << matrix.size() << " " << matrix[0].size() << "\n";
    for (const auto& row : matrix) {
        for (double val : row) {
            file << std::setprecision(15) << val << " ";
        }
        file << "\n";
    }
    file.close();
}

// 保存向量到文件
void MatrixUtils::saveVectorToFile(const Vector& vector, const std::string& filename) {
    std::ofstream file(filename);
    if (!file.is_open()) {
        std::cerr << "Error: Cannot open file " << filename << std::endl;
        return;
    }
    
    file << vector.size() << "\n";
    for (double val : vector) {
        file << std::setprecision(15) << val << " ";
    }
    file << "\n";
    file.close();
}

// 从文件加载矩阵
Matrix MatrixUtils::loadMatrixFromFile(const std::string& filename) {
    std::ifstream file(filename);
    if (!file.is_open()) {
        std::cerr << "Error: Cannot open file " << filename << std::endl;
        return Matrix();
    }
    
    int rows, cols;
    file >> rows >> cols;
    Matrix matrix(rows, Vector(cols));
    
    for (int i = 0; i < rows; ++i) {
        for (int j = 0; j < cols; ++j) {
            file >> matrix[i][j];
        }
    }
    file.close();
    return matrix;
}

// 从文件加载向量
Vector MatrixUtils::loadVectorFromFile(const std::string& filename) {
    std::ifstream file(filename);
    if (!file.is_open()) {
        std::cerr << "Error: Cannot open file " << filename << std::endl;
        return Vector();
    }
    
    int size;
    file >> size;
    Vector vector(size);
    
    for (int i = 0; i < size; ++i) {
        file >> vector[i];
    }
    file.close();
    return vector;
}

// 比较两个向量
bool MatrixUtils::compareVectors(const Vector& v1, const Vector& v2, double tolerance) {
    if (v1.size() != v2.size()) return false;
    
    for (size_t i = 0; i < v1.size(); ++i) {
        if (std::abs(v1[i] - v2[i]) > tolerance) {
            return false;
        }
    }
    return true;
}

// 计算向量误差
double MatrixUtils::calculateError(const Vector& v1, const Vector& v2) {
    if (v1.size() != v2.size()) return -1.0;
    
    double max_error = 0.0;
    for (size_t i = 0; i < v1.size(); ++i) {
        max_error = std::max(max_error, std::abs(v1[i] - v2[i]));
    }
    return max_error;
}

// 保存性能测试结果
void MatrixUtils::savePerformanceResults(const std::vector<PerformanceResult>& results, 
                                       const std::string& filename) {
    std::ofstream file(filename);
    if (!file.is_open()) {
        std::cerr << "Error: Cannot open file " << filename << std::endl;
        return;
    }
    
    file << "Method,MatrixSize,NumThreads,ExecutionTime,Speedup,Efficiency\n";
    for (const auto& result : results) {
        file << result.method << "," << result.matrix_size << "," 
             << result.num_threads << "," << result.execution_time << ","
             << result.speedup << "," << result.efficiency << "\n";
    }
    file.close();
}

// 计时器实现
void Timer::start() {
    start_time = std::chrono::high_resolution_clock::now();
}

double Timer::stop() {
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
    return duration.count() / 1000000.0; // 转换为秒
}
